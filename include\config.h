#ifndef CONFIG_H
#define CONFIG_H

/* ------------------------------------------------------------------------
 * 基础屏幕参数配置
 * ------------------------------------------------------------------------ */
#define SCREEN_WIDTH 64                                    // 屏幕宽度（列数）
#define SCREEN_HEIGHT 32                                   // 屏幕高度（行数）
#define PANEL_CHAIN_LENGTH 1                               // 链式面板数量
#define TOTAL_LED_COUNT (SCREEN_WIDTH * SCREEN_HEIGHT * 3) // 总LED数量

/* ------------------------------------------------------------------------
 * HUB75 LED矩阵屏引脚配置
 * ------------------------------------------------------------------------ */
// 数据引脚 (RGB1)
#define PIN_R1 25    // 红色数据线1
#define PIN_G1 26    // 绿色数据线1
#define PIN_B1 27    // 蓝色数据线1

// 数据引脚 (RGB2) - 用于下半部分扫描
#define PIN_R2 14    // 红色数据线2
#define PIN_G2 12    // 绿色数据线2
#define PIN_B2 13    // 蓝色数据线2

// 地址选择引脚
#define PIN_A 23     // 地址线A
#define PIN_B 22     // 地址线B
#define PIN_C 21      // 地址线C
#define PIN_D 19     // 地址线D
#define PIN_E -1     // 地址线E (可选，用于更大的面板)

// 控制引脚
#define PIN_LAT 4    // 锁存信号 (Latch)
#define PIN_OE 15    // 输出使能 (Output Enable，低电平有效)
#define PIN_CLK 16   // 时钟信号 (Clock)

/* ------------------------------------------------------------------------
 * LED矩阵屏驱动配置
 * ------------------------------------------------------------------------ */
#define MATRIX_BRIGHTNESS 80        // 默认亮度 (0-255)
#define MATRIX_COLOR_DEPTH 8        // 颜色深度 (位)
#define MATRIX_REFRESH_RATE 120     // 刷新率 (Hz)
#define MATRIX_DRIVER_CHIP FM6126A  // 驱动芯片类型

/* ------------------------------------------------------------------------
 * 蓝牙协议配置
 * ------------------------------------------------------------------------ */
#define BT_FRAME_HEADER_1 0xAA     // 帧头第1字节
#define BT_FRAME_HEADER_2 0x55     // 帧头第2字节
#define BT_FRAME_TAIL_1 0x0D       // 帧尾第1字节
#define BT_FRAME_TAIL_2 0x0A       // 帧尾第2字节
#define BT_CMD_SET_TEXT 0x01       // 设置文本命令
#define BT_CMD_SET_ANIMATION 0x02  // 设置动画命令
#define BT_CMD_SET_COLOR 0x03      // 设置颜色命令
#define BT_CMD_SET_BRIGHTNESS 0x04 // 设置亮度命令
#define BT_CMD_SET_EFFECT 0x05     // 设置特效命令
#define BT_CMD_FILE_START 0x10     // 文件传输开始命令
#define BT_CMD_FILE_DATA 0x11      // 文件数据传输命令
#define BT_CMD_FILE_END 0x12       // 文件传输结束命令
#define BT_CMD_FILE_LIST 0x13      // 获取文件列表命令
#define BT_CMD_FILE_DELETE 0x14    // 删除文件命令
#define BT_CMD_PLAY_GIF 0x15       // 播放指定GIF命令
#define BT_CMD_SET_GIF_OFFSET 0x16 // 设置GIF偏移量命令

/* ------------------------------------------------------------------------
 * 参数定义
 * ------------------------------------------------------------------------ */
#define BT_COLOR_TARGET_TEXT 0x01       // 选择文本
#define BT_COLOR_TARGET_BACKGROUND 0x02 // 选择背景
#define BT_COLOR_MODE_FIXED 0x01        // 固定色
#define BT_COLOR_MODE_GRADIENT 0x02     // 渐变色
#define BT_COLOR_DATA_LEN 5             // 颜色命令数据长度（1+1+3字节）
#define BT_BRIGHTNESS_DATA_LEN 1        // 亮度命令数据长度（1字节）
#define BT_EFFECT_DATA_LEN 2            // 特效命令数据长度（2字节）

/* ------------------------------------------------------------------------
 * 特效类型定义
 * ------------------------------------------------------------------------ */
#define BT_EFFECT_FIXED 0x00        // 固定显示
#define BT_EFFECT_SCROLL_LEFT 0x01  // 左移滚动
#define BT_EFFECT_SCROLL_RIGHT 0x02 // 右移滚动
#define BT_EFFECT_BLINK 0x03        // 闪烁特效
#define BT_EFFECT_BREATHE 0x04      // 呼吸特效
#define BT_EFFECT_SNOW 0x05         // 雪花特效
#define BT_EFFECT_LASER 0x06        // 镭射特效

/* ------------------------------------------------------------------------
 * 性能配置
 * ------------------------------------------------------------------------ */
#define BT_MAX_FRAME_SIZE 8192      // 最大帧大小
#define BT_FRAME_TIMEOUT_MS 5000    // 帧超时时间（毫秒）
#define BT_MAX_ERROR_COUNT 100      // 最大错误计数

/* ------------------------------------------------------------------------
 * 文件传输配置
 * ------------------------------------------------------------------------ */
#define FILE_TRANSFER_CHUNK_SIZE 4096   // 每次传输的数据块大小
#define FILE_MAX_NAME_LENGTH 64         // 文件名最大长度
#define FILE_MAX_SIZE (2 * 1024 * 1024) // 单个文件最大大小 (2MB)
#define FILE_TRANSFER_TIMEOUT 30000     // 文件传输超时时间 (毫秒)

/* ------------------------------------------------------------------------
 * GIF动画播放配置
 * ------------------------------------------------------------------------ */
#define GIF_DEFAULT_FRAME_DELAY 50  // 默认GIF帧延迟 (毫秒)
#define GIF_MAX_WIDTH 64            // GIF最大宽度
#define GIF_MAX_HEIGHT 32           // GIF最大高度
#define GIF_STARTUP_FILE "/gifs/cartoon.gif"  // 开机播放的GIF文件
#define GIF_STORAGE_PATH "/gifs/"   // GIF文件存储路径

/* ------------------------------------------------------------------------
 * GIF显示位置配置
 * ------------------------------------------------------------------------ */
#define GIF_DEFAULT_OFFSET_X 0      // 默认GIF显示X偏移量
#define GIF_DEFAULT_OFFSET_Y 0      // 默认GIF显示Y偏移量

/* ------------------------------------------------------------------------
 * 文件系统配置
 * ------------------------------------------------------------------------ */
#define FS_FORMAT_ON_FAIL true      // 挂载失败时是否格式化
#define FS_MAX_OPEN_FILES 5         // 最大同时打开文件数

/* ------------------------------------------------------------------------
 * 串口和蓝牙配置
 * ------------------------------------------------------------------------ */
#define SERIAL_BAUD_RATE 115200     // 串口波特率
#define BT_DEVICE_NAME "ESP32-BT-Slave"  // 蓝牙设备名称

/* ------------------------------------------------------------------------
 * 系统配置
 * ------------------------------------------------------------------------ */
#define SYSTEM_STARTUP_DELAY 1000   // 系统启动延迟 (毫秒)
#define WATCHDOG_TIMEOUT 30000      // 看门狗超时时间 (毫秒)

#endif // CONFIG_H
