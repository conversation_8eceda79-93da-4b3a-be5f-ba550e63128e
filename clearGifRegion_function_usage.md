# clearGifRegion 函数使用说明

## 函数签名
```cpp
static void clearGifRegion(int16_t x1, int16_t y1, int16_t x2, int16_t y2, uint16_t bg_color = COLOR_BLACK);
```

## 功能说明
清除指定区域的GIF缓冲区数据，解决关闭GIF后出现最后一帧数据残留导致的残影问题。

## 参数说明
- `x1, y1`: 区域左上角坐标
- `x2, y2`: 区域右下角坐标（包含边界）
- `bg_color`: 背景颜色，默认为黑色 (COLOR_BLACK)

## 工作原理
1. **清除GIF缓冲区**：将指定区域的 `gif_backup[]` 数组设置为背景色
2. **立即更新显示**：如果该位置没有文字遮罩，立即用背景色更新屏幕显示
3. **保护文字显示**：如果该位置有文字，不影响文字显示

## 使用场景

### 1. 关闭GIF时清除残影
```cpp
// 在stopGIF函数中使用
void stopGIF() {
    gif.close();
    gifPlaying = false;
    
    // 清除GIF区域的缓冲区数据，避免残影
    EnhancedTextPixelMask::clearGifRegion(0, 0, 15, 31, COLOR_BLACK);
    
    printf("GIF stopped and region cleared\n");
}
```

### 2. 清除特定区域的GIF数据
```cpp
// 清除时间区域的GIF残留
EnhancedTextPixelMask::clearGifRegion(16, 0, 63, 15, COLOR_BLACK);

// 清除星期区域的GIF残留
EnhancedTextPixelMask::clearGifRegion(16, 16, 63, 31, COLOR_BLACK);

// 清除大屏区域的GIF残留
EnhancedTextPixelMask::clearGifRegion(16, 0, 63, 31, COLOR_BLACK);
```

### 3. 使用自定义背景色
```cpp
// 使用深蓝色作为背景
EnhancedTextPixelMask::clearGifRegion(0, 0, 63, 31, 0x001F);

// 使用深红色作为背景
EnhancedTextPixelMask::clearGifRegion(0, 0, 63, 31, 0x8000);
```

## 预定义区域坐标

### 64×32屏幕的标准区域
```cpp
// GIF区域 (左侧)
EnhancedTextPixelMask::clearGifRegion(0, 0, 15, 31);

// 时间区域 (右上)
EnhancedTextPixelMask::clearGifRegion(16, 0, 63, 15);

// 星期区域 (右下)
EnhancedTextPixelMask::clearGifRegion(16, 16, 63, 31);

// 大屏区域 (整个右侧)
EnhancedTextPixelMask::clearGifRegion(16, 0, 63, 31);

// 全屏区域
EnhancedTextPixelMask::clearGifRegion(0, 0, 63, 31);
```

## 调试输出
函数会输出调试信息到串口：
```
Clearing GIF region: (0,0) to (15,31)
GIF region cleared with background color 0x0000
```

## 注意事项
1. **坐标范围**：确保坐标在屏幕范围内 (0-63, 0-31)
2. **系统状态**：函数会自动检查系统是否就绪
3. **文字保护**：不会影响已显示的文字内容
4. **边界包含**：x2, y2 坐标是包含在清除范围内的

## 与其他函数的配合使用

### 配合模式切换
```cpp
// 切换显示模式时清除对应区域
void setDisplayMode(DisplayMode mode) {
    if (mode == DISPLAY_MODE_BIG) {
        // 清除小屏区域的GIF残留
        EnhancedTextPixelMask::clearGifRegion(16, 0, 63, 15);  // 时间区域
        EnhancedTextPixelMask::clearGifRegion(16, 16, 63, 31); // 星期区域
    } else {
        // 清除大屏区域的GIF残留
        EnhancedTextPixelMask::clearGifRegion(16, 0, 63, 31);  // 大屏区域
    }
    
    // 原有的模式切换逻辑...
}
```

### 配合GIF切换
```cpp
// 切换GIF时清除旧的缓冲区数据
void switchGIF(const char* new_gif) {
    // 清除旧GIF的缓冲区数据
    EnhancedTextPixelMask::clearGifRegion(0, 0, 15, 31, COLOR_BLACK);
    
    // 播放新GIF
    playGIF(new_gif);
}
```

这个函数完美解决了GIF关闭后的残影问题，为分区控制和多GIF显示奠定了基础！
