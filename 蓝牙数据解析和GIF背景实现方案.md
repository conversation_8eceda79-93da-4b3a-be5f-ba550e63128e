# 蓝牙数据解析和GIF背景文字前景实现方案

## 一、蓝牙数据解析示例

### **数据格式定义**
```cpp
// 蓝牙接收数据数组格式（6个字节）
// data[0]: 大小屏模式选择 (0=小屏, 1=大屏)
// data[1]: 时钟数字 (0-23)
// data[2]: 分钟数字 (0-59)
// data[3]: 星期选择 (0-6, 0=周日)
// data[4]: 中英文语言选择 (0=中文, 1=英文)
// data[5]: 字符颜色 (0-7, 预定义颜色索引)
```

### **数据解析函数示例**
```cpp
// 预定义颜色表
const uint16_t color_table[8] = {
    COLOR_WHITE,   // 0: 白色
    COLOR_RED,     // 1: 红色
    COLOR_GREEN,   // 2: 绿色
    COLOR_BLUE,    // 3: 蓝色
    COLOR_YELLOW,  // 4: 黄色
    COLOR_CYAN,    // 5: 青色
    COLOR_MAGENTA, // 6: 洋红
    COLOR_ORANGE   // 7: 橙色
};

// 蓝牙数据解析和执行函数
void parseAndExecuteBluetoothData(uint8_t* data, size_t length) {
    if (length < 6) {
        printf("Error: Bluetooth data length insufficient: %d\n", length);
        return;
    }
    
    printf("=== Parsing Bluetooth Data ===\n");
    printf("Raw data: [%d, %d, %d, %d, %d, %d]\n", 
           data[0], data[1], data[2], data[3], data[4], data[5]);
    
    // 解析数据
    uint8_t display_mode = data[0];
    uint8_t hour = data[1];
    uint8_t minute = data[2];
    uint8_t weekday = data[3];
    uint8_t language = data[4];
    uint8_t color_index = data[5];
    
    // 数据验证
    if (hour > 23) {
        printf("Error: Invalid hour: %d\n", hour);
        return;
    }
    if (minute > 59) {
        printf("Error: Invalid minute: %d\n", minute);
        return;
    }
    if (weekday > 6) {
        printf("Error: Invalid weekday: %d\n", weekday);
        return;
    }
    if (language > 1) {
        printf("Error: Invalid language: %d\n", language);
        return;
    }
    if (color_index > 7) {
        printf("Error: Invalid color index: %d\n", color_index);
        return;
    }
    
    printf("Parsed data: Mode=%s, Time=%02d:%02d, Weekday=%d, Lang=%s, Color=%d\n",
           (display_mode == 0) ? "SMALL" : "BIG",
           hour, minute, weekday,
           (language == 0) ? "CHINESE" : "ENGLISH",
           color_index);
    
    // 设置时间和星期
    time_info.hour = hour;
    time_info.minute = minute;
    time_info.weekday = weekday;
    
    // 设置语言
    WeekdayLanguage lang = (language == 0) ? LANG_CHINESE : LANG_ENGLISH;
    setBigScreenLanguage(lang);
    current_display_language = lang;
    
    // 设置颜色
    uint16_t text_color = color_table[color_index];
    setBigScreenTimeColor(text_color);
    setBigScreenWeekdayColor(text_color);
    
    // 设置显示模式并执行
    if (display_mode == 0) {
        // 小屏模式
        printf("Executing: Switch to small screen mode\n");
        setDisplayMode(DISPLAY_MODE_SMALL);
        
        // 更新小屏显示
        drawTimeToRegion(&display_regions[REGION_TIME], hour, minute);
        drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], weekday, lang);
        
    } else {
        // 大屏模式
        printf("Executing: Switch to big screen mode\n");
        setDisplayMode(DISPLAY_MODE_BIG);
        
        // 强制显示星期（避免时间切换）
        big_screen_state = BIG_SCREEN_WEEKDAY;
        drawBigScreenWeekday();
    }
    
    printf("=== Bluetooth Data Execution Completed ===\n");
}

// 模拟蓝牙接收数据的测试函数
void simulateBluetoothData() {
    printf("\n==================== BLUETOOTH DATA SIMULATION ====================\n");
    
    // 模拟数据1：大屏模式，14:30，周六，中文，红色
    uint8_t test_data1[] = {1, 14, 30, 6, 0, 1};
    printf("Test 1: Big screen, 14:30, Saturday, Chinese, Red\n");
    parseAndExecuteBluetoothData(test_data1, 6);
    delay(3000);
    
    // 模拟数据2：小屏模式，09:15，周二，英文，蓝色
    uint8_t test_data2[] = {0, 9, 15, 2, 1, 3};
    printf("\nTest 2: Small screen, 09:15, Tuesday, English, Blue\n");
    parseAndExecuteBluetoothData(test_data2, 6);
    delay(3000);
    
    // 模拟数据3：大屏模式，23:45，周五，英文，绿色
    uint8_t test_data3[] = {1, 23, 45, 5, 1, 2};
    printf("\nTest 3: Big screen, 23:45, Friday, English, Green\n");
    parseAndExecuteBluetoothData(test_data3, 6);
    delay(3000);
    
    printf("==================== SIMULATION COMPLETED ====================\n");
}
```

### **集成到现有蓝牙系统**
```cpp
// 在现有的蓝牙接收回调中调用
void onBluetoothDataReceived(uint8_t* data, size_t length) {
    // 现有的蓝牙处理逻辑...
    
    // 检查是否是显示控制数据（通过特定标识符或数据长度判断）
    if (length == 6) {
        parseAndExecuteBluetoothData(data, length);
    } else {
        // 其他类型的蓝牙数据处理...
    }
}
```

## 二、GIF背景文字前景实现方案

### **方案对比分析**

#### **您的方案：GIF渲染时检查字体占用**
**优点**：
- 实现相对简单
- GIF和文字完美融合

**缺点**：
- 高度依赖GIF存在
- 没有GIF时文字无法显示
- 性能开销大（每个像素都要检查）
- 字体数据需要预先计算并存储

#### **推荐方案：分层渲染系统**

### **方案一：双缓冲分层渲染（推荐）**

#### **核心思路**
1. **背景层**：GIF动画渲染
2. **前景层**：文字渲染
3. **合成层**：背景+前景合成最终显示

#### **技术实现**
```cpp
// 分层渲染系统
class LayeredRenderer {
private:
    uint16_t* background_buffer;  // 背景缓冲区（GIF）
    uint16_t* foreground_buffer;  // 前景缓冲区（文字）
    uint16_t* display_buffer;     // 最终显示缓冲区
    bool* text_mask;              // 文字遮罩（标记哪些像素有文字）
    
public:
    void renderBackground() {
        // 渲染GIF到背景缓冲区
        if (gif_available) {
            renderGIFToBuffer(background_buffer);
        } else {
            // 没有GIF时使用纯色背景
            fillBuffer(background_buffer, background_color);
        }
    }
    
    void renderForeground() {
        // 清空前景缓冲区和遮罩
        clearBuffer(foreground_buffer);
        clearMask(text_mask);
        
        // 渲染文字到前景缓冲区
        renderTextToBuffer(foreground_buffer, text_mask);
    }
    
    void composite() {
        // 合成背景和前景
        for (int i = 0; i < SCREEN_PIXELS; i++) {
            if (text_mask[i]) {
                // 有文字的地方显示前景
                display_buffer[i] = foreground_buffer[i];
            } else {
                // 没有文字的地方显示背景
                display_buffer[i] = background_buffer[i];
            }
        }
    }
    
    void update() {
        renderBackground();
        renderForeground();
        composite();
        displayToScreen(display_buffer);
    }
};
```

#### **优点**
- 完全独立：GIF和文字互不依赖
- 灵活性高：可以单独控制背景和前景
- 性能优化：只在需要时重新渲染
- 扩展性强：可以添加更多图层

#### **缺点**
- 内存占用较大（需要多个缓冲区）
- 实现复杂度较高

### **方案二：智能像素替换（内存优化版）**

#### **核心思路**
1. **正常渲染GIF**
2. **文字渲染时替换对应像素**
3. **使用文字遮罩记录替换位置**

#### **技术实现**
```cpp
class SmartPixelRenderer {
private:
    bool* text_mask;              // 文字像素遮罩
    uint16_t* original_pixels;    // 原始背景像素备份
    
public:
    void renderGIF() {
        // 正常渲染GIF
        updateGIF();
    }
    
    void renderTextOverlay() {
        // 备份将要被文字覆盖的像素
        backupPixelsUnderText();
        
        // 直接在屏幕上绘制文字
        drawTextWithMask();
    }
    
    void restoreBackground() {
        // 恢复被文字覆盖的背景像素
        for (int i = 0; i < SCREEN_PIXELS; i++) {
            if (text_mask[i]) {
                restorePixel(i, original_pixels[i]);
                text_mask[i] = false;
            }
        }
    }
    
    void update() {
        // 先恢复背景
        restoreBackground();
        // 渲染GIF
        renderGIF();
        // 叠加文字
        renderTextOverlay();
    }
};
```

#### **优点**
- 内存占用较小
- 实现相对简单
- 性能较好

#### **缺点**
- 需要精确的像素备份和恢复
- 文字变化时需要重新计算遮罩

### **方案三：区域分离渲染（最简单）**

#### **核心思路**
1. **GIF区域**：专门显示GIF
2. **文字区域**：专门显示文字
3. **透明混合**：文字区域使用半透明背景

#### **技术实现**
```cpp
void renderWithRegionSeparation() {
    // 渲染GIF到指定区域
    renderGIFToRegion(&gif_region);
    
    // 在文字区域绘制半透明背景
    drawTransparentBackground(&text_region, alpha_value);
    
    // 在半透明背景上绘制文字
    drawTextToRegion(&text_region);
}
```

#### **优点**
- 实现最简单
- 兼容现有区域系统
- 内存占用最小

#### **缺点**
- 文字和GIF不能完全重叠
- 视觉效果不如完全叠加

### **推荐实现方案**

#### **阶段一：简单实现（方案三）**
- 使用现有区域系统
- GIF在左侧，文字在右侧半透明区域
- 快速实现，验证效果

#### **阶段二：完整实现（方案二）**
- 实现智能像素替换
- 文字完全叠加在GIF上
- 平衡性能和效果

#### **阶段三：高级实现（方案一）**
- 完整的分层渲染系统
- 支持多层叠加
- 最佳视觉效果

### **具体实现建议**

#### **1. 文字遮罩生成**
```cpp
void generateTextMask(bool* mask, const char* text, int x, int y) {
    // 预渲染文字到临时缓冲区
    // 生成对应的像素遮罩
    // 标记哪些像素被文字占用
}
```

#### **2. 背景保护机制**
```cpp
void protectBackground() {
    // 在文字渲染前保存背景像素
    // 文字更新时恢复原始背景
    // 避免文字残留问题
}
```

#### **3. 性能优化**
- 只在文字内容变化时重新生成遮罩
- 使用增量更新，只更新变化的区域
- 缓存常用的文字渲染结果

### **总结**

**推荐采用方案二（智能像素替换）**，因为它：
1. 平衡了实现复杂度和效果
2. 内存占用合理
3. 与现有系统兼容性好
4. 可以逐步优化和扩展

这样既能实现GIF背景上显示文字的效果，又不会因为没有GIF而影响文字显示。
