// Created with image_to_c
// https://github.com/bitbank2/image_to_c
//
// green
// Data size = 63 bytes
//
// for non-Arduino builds...
#ifndef PROGMEM
#define PROGMEM
#endif
const uint8_t green[] PROGMEM = {
	0x47,0x49,0x46,0x38,0x37,0x61,0x20,0x00,0x20,0x00,0x80,0x01,0x00,0x13,0xe9,0x4b,
	0xff,0xff,0xff,0x2c,0x00,0x00,0x00,0x00,0x20,0x00,0x20,0x00,0x00,0x02,0x1e,0x84,
	0x8f,0xa9,0xcb,0xed,0x0f,0xa3,0x9c,0xb4,0xda,0x8b,0xb3,0xde,0xbc,0xfb,0x0f,0x86,
	0xe2,0x48,0x96,0xe6,0x89,0xa6,0xea,0xca,0xb6,0xee,0x0b,0x9b,0x05,0x00,0x3b};
