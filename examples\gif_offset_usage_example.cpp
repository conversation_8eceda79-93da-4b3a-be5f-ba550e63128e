/*
 * GIF显示偏移量功能使用示例
 * 
 * 本文件展示了如何使用新增的GIF显示偏移量功能
 * 包括基本使用方法、动态调整和蓝牙协议控制
 */

#include "gif_player.h"
#include "config.h"

// ========================================
// 基本使用示例
// ========================================

void example_basic_usage() 
{
    Serial.println("=== 基本使用示例 ===");
    
    // 1. 设置GIF显示在屏幕中央（假设GIF是32x16像素，屏幕是64x32）
    setGifDisplayOffset(16, 8);  // 向右偏移16像素，向下偏移8像素
    
    // 2. 播放GIF
    playGIF("/gifs/kaiji.gif");
    
    delay(5000);  // 播放5秒
    
    // 3. 重置为默认位置（左上角）
    resetGifDisplayOffset();
    
    Serial.println("基本使用示例完成");
}

// ========================================
// 动态调整示例
// ========================================

void example_dynamic_adjustment() 
{
    Serial.println("=== 动态调整示例 ===");
    
    // 播放GIF
    playGIF("/gifs/cartoon.gif");
    
    // 模拟动态移动效果
    for (int i = 0; i < 10; i++) {
        // 每秒向右移动2像素
        setGifDisplayOffset(i * 2, 0);
        delay(1000);
    }
    
    // 模拟上下移动
    for (int i = 0; i < 5; i++) {
        setGifDisplayOffset(20, i * 3);
        delay(1000);
    }
    
    // 重置位置
    resetGifDisplayOffset();
    
    Serial.println("动态调整示例完成");
}

// ========================================
// 获取当前偏移量示例
// ========================================

void example_get_offset() 
{
    Serial.println("=== 获取偏移量示例 ===");
    
    // 设置一个偏移量
    setGifDisplayOffset(10, 5);
    
    // 获取当前偏移量
    int current_x, current_y;
    getGifDisplayOffset(&current_x, &current_y);
    
    Serial.printf("当前偏移量: X=%d, Y=%d\n", current_x, current_y);
    
    // 基于当前偏移量进行调整
    setGifDisplayOffset(current_x + 5, current_y - 2);
    
    getGifDisplayOffset(&current_x, &current_y);
    Serial.printf("调整后偏移量: X=%d, Y=%d\n", current_x, current_y);
    
    Serial.println("获取偏移量示例完成");
}

// ========================================
// 边界测试示例
// ========================================

void example_boundary_test() 
{
    Serial.println("=== 边界测试示例 ===");
    
    // 测试超出屏幕范围的偏移量
    Serial.println("测试超出范围的偏移量...");
    
    // 尝试设置超出屏幕的偏移量
    setGifDisplayOffset(100, 50);  // 会被限制在合理范围内
    
    int x, y;
    getGifDisplayOffset(&x, &y);
    Serial.printf("实际设置的偏移量: X=%d, Y=%d\n", x, y);
    
    // 测试负偏移量
    setGifDisplayOffset(-10, -5);
    getGifDisplayOffset(&x, &y);
    Serial.printf("负偏移量: X=%d, Y=%d\n", x, y);
    
    // 播放GIF测试显示效果
    playGIF("/gifs/matrix-spin.gif");
    delay(3000);
    
    resetGifDisplayOffset();
    Serial.println("边界测试示例完成");
}

// ========================================
// 多种显示位置预设示例
// ========================================

void example_preset_positions() 
{
    Serial.println("=== 预设位置示例 ===");
    
    const char* gif_file = "/gifs/three.gif";
    
    // 左上角（默认）
    Serial.println("显示在左上角");
    resetGifDisplayOffset();
    playGIF(gif_file);
    delay(2000);
    
    // 右上角
    Serial.println("显示在右上角");
    setGifDisplayOffset(32, 0);  // 假设GIF宽度为32
    delay(2000);
    
    // 左下角
    Serial.println("显示在左下角");
    setGifDisplayOffset(0, 16);  // 假设GIF高度为16
    delay(2000);
    
    // 右下角
    Serial.println("显示在右下角");
    setGifDisplayOffset(32, 16);
    delay(2000);
    
    // 中央
    Serial.println("显示在中央");
    setGifDisplayOffset(16, 8);
    delay(2000);
    
    resetGifDisplayOffset();
    Serial.println("预设位置示例完成");
}

// ========================================
// 蓝牙协议使用说明
// ========================================

/*
蓝牙协议格式说明：

命令：BT_CMD_SET_GIF_OFFSET (0x16)
数据格式：4字节
- 字节0-1: X偏移量 (16位有符号整数，大端序)
- 字节2-3: Y偏移量 (16位有符号整数，大端序)

示例：
设置偏移量为 (10, 5)
发送数据：AA 55 16 04 00 0A 00 05 0D 0A
解释：
- AA 55: 帧头
- 16: 命令 (BT_CMD_SET_GIF_OFFSET)
- 04: 数据长度
- 00 0A: X偏移量 = 10
- 00 05: Y偏移量 = 5
- 0D 0A: 帧尾

设置偏移量为 (-5, -3)
发送数据：AA 55 16 04 FF FB FF FD 0D 0A
解释：
- FF FB: X偏移量 = -5 (补码表示)
- FF FD: Y偏移量 = -3 (补码表示)
*/

// ========================================
// 主函数示例
// ========================================

void gif_offset_examples() 
{
    Serial.println("开始GIF偏移量功能演示...");
    
    // 确保LED矩阵已初始化
    if (!initLEDMatrix()) {
        Serial.println("LED矩阵初始化失败！");
        return;
    }
    
    // 运行各种示例
    example_basic_usage();
    delay(1000);
    
    example_get_offset();
    delay(1000);
    
    example_boundary_test();
    delay(1000);
    
    example_preset_positions();
    delay(1000);
    
    example_dynamic_adjustment();
    
    Serial.println("所有示例演示完成！");
}
