#include "BluetoothSerial.h"
#include <ESP32-HUB75-MatrixPanel-I2S-DMA.h>
#include "bluetooth_protocol.h"
#include "LittleFS.h"
#include "AnimatedGIF.h"
#include "config.h"
#include "file_handler.h"
#include "gif_player.h"

String device_name = BT_DEVICE_NAME;
BluetoothProtocolParser btParser; // 蓝牙协议解析器
BluetoothFrame currentFrame;      // 当前解析的帧

// 延迟加载相关变量
static bool startupGifChanged = false;
static unsigned long startupTime = 0;

// 这些变量已移动到对应的模块文件中

// Check if Bluetooth is available
#if !defined(CONFIG_BT_ENABLED) || !defined(CONFIG_BLUEDROID_ENABLED)
#error Bluetooth is not enabled! Please run `make menuconfig` to and enable it
#endif

// Check Serial Port Profile
#if !defined(CONFIG_BT_SPP_ENABLED)
#error Serial Port Profile for Bluetooth is not available or not enabled. It is only available for the ESP32 chip.
#endif

BluetoothSerial SerialBT;

// 函数声明
void handleParseResult(ParseResult result);
void processBluetoothCommand(const BluetoothFrame &frame);
void handleTextCommand(const BluetoothFrame &frame);
void handleColorCommand(const BluetoothFrame &frame);
void handleBrightnessCommand(const BluetoothFrame &frame);
void handleEffectCommand(const BluetoothFrame &frame);
void handleAnimationCommand(const BluetoothFrame &frame);
void testOverlaySystem();
const char *getEffectName(uint8_t type);

// 函数声明已移动到对应的头文件中

void setup()
{
    // 1. 初始化串口和蓝牙
    Serial.begin(SERIAL_BAUD_RATE);
    SerialBT.begin(device_name); // Bluetooth device name
    // SerialBT.deleteAllBondedDevices(); // Uncomment this to delete paired devices; Must be called after begin
    //setGifDisplayOffset(0, 8);
    // 2. 初始化LittleFS文件系统
    if (!initLittleFS()) {
        Serial.println("Error: LittleFS initialization failed!");
        return;
    }

    // 创建gifs目录
    createGifsDirectory();

    // 3. 初始化LED矩阵屏
    if (!initLEDMatrix()) {
        Serial.println("Error: LED matrix initialization failed!");
        return;
    }

    // 4. 初始化显示区域系统
    initDisplayRegions();
    setupTimeRegions();

    // 5. 初始化增强文字像素遮罩系统
    if (!EnhancedTextPixelMask::initSystem()) {
        printf("ERROR: Enhanced mask system initialization failed!\n");
        printf("Falling back to traditional display mode...\n");
        // 可以选择继续运行传统模式，或者停止程序
    } else {
        printf("Enhanced mask system ready for overlay display!\n");
    }

    // 6. 预检查GIF文件
    checkGifFiles();

    // 7. 播放启动GIF动画
    // if (!playGIF(GIF_STARTUP_FILE)) {
    //     Serial.println("Warning: Unable to play startup animation");
    // }

    // 8. 显示系统信息
    Serial.println("=== ESP32 Bluetooth LED Controller ===");
    Serial.printf("Device name: %s\n", device_name.c_str());
    Serial.println("Bluetooth protocol parser initialized");
    Serial.println("Display regions system initialized");
    Serial.println("Waiting for Bluetooth connection and data...");

    // 记录启动时间，用于延迟切换GIF
    startupTime = millis();
    Serial.println("Startup animation will change to R.gif in 5 seconds...");

    // 初始化大屏模式参数
    setBigScreenSwitchInterval(5000);  // 5秒切换间隔
    setBigScreenColorTheme(COLOR_RED, COLOR_BLUE, COLOR_BLACK);

    // 在 setup() 中或通过蓝牙命令设置
    setTimeAndWeekdayWithLang(23, 59, 6, LANG_ENGLISH);  // 周六 23:59

    // 测试大屏模式（10秒后自动启动）
    Serial.println("Big screen mode test will start in 10 seconds...");
     testBigScreenMode(23, 59, 6, LANG_ENGLISH);
    //dma_display->setBrightness8(10);
    // 等待一分钟，观察自动切换到周二 00:00
}

void loop()
{
   

    // // 在启动5秒后切换GIF
    // if (!startupGifChanged && (millis() - startupTime > 5000)) {
    //     Serial.println("5 seconds elapsed, changing to R.gif...");
    //     changeStartupGIF("pk.gif");
    //     startupGifChanged = true;
    // }

  
       
 
    // 更新GIF动画
    //updateGIF();

    // 执行叠层系统测试（仅执行一次）
    testOverlaySystem();

    // 根据显示模式更新显示
    static unsigned long last_loop_debug = 0;
    static unsigned long loop_count = 0;

    loop_count++;

    // 每10秒输出一次基本状态信息
    if (millis() - last_loop_debug > 10000) {
        extern TimeDisplayInfo time_info;
        printf("=== System Status === Mode: %s, Time: %02d:%02d, Weekday: %d\n",
               (current_display_mode == DISPLAY_MODE_SMALL) ? "SMALL" : "BIG",
               time_info.hour, time_info.minute, time_info.weekday);
        last_loop_debug = millis();
    }

    if (current_display_mode == DISPLAY_MODE_SMALL) {
        // 小屏模式：使用原有逻辑
        updateTimeDisplay();
    } else {
        // 大屏模式：使用新逻辑
        updateBigScreenDisplay();
    }

    // 更新所有区域
    updateAllRegions();

    // 处理串口到蓝牙的数据转发
    if (Serial.available())
    {
        SerialBT.write(Serial.read());
    }

    // 处理蓝牙接收的数据并解析协议 - 批量处理以避免缓冲区溢出
    while (SerialBT.available())
    {
        uint8_t receivedByte = SerialBT.read();
        ParseResult result = btParser.parseByte(receivedByte, currentFrame);

        handleParseResult(result);

        // 如果缓冲区还有很多数据，继续处理，但避免阻塞太久
        if (SerialBT.available() > 100) {
            // 处理多个字节后短暂让出CPU
            if (millis() % 10 == 0) {
                yield();
            }
        }
    }

    // 检查解析器超时
    if (btParser.isFrameTimeout())
    {
        Serial.println("Warning: Frame receive timeout, resetting parser");
        btParser.reset();
    }

    // 检查文件传输超时
    if (fileTransferActive && (millis() - fileTransferStartTime > FILE_TRANSFER_TIMEOUT))
    {
        Serial.println("Warning: File transfer timeout, aborting transfer");
        abortFileTransfer();
    }
}

// 处理解析结果
void handleParseResult(ParseResult result)
{
    switch (result)
    {
    case ParseResult::FRAME_COMPLETE:
        Serial.printf("Received complete frame - Command: 0x%02X, Data length: %d\n",
                      currentFrame.command, currentFrame.dataLength);
        processBluetoothCommand(currentFrame);
        btParser.reset(); // 重置解析器准备下一帧
        break;

    case ParseResult::FRAME_ERROR:
        Serial.println("Error: Frame format error");
        break;

    case ParseResult::INVALID_COMMAND:
        Serial.println("Error: Invalid command");
        break;

    case ParseResult::DATA_TOO_LONG:
        Serial.println("Error: Data too long");
        break;

    case ParseResult::NEED_MORE_DATA:
        // 继续等待更多数据，无需处理
        break;

    default:
        Serial.printf("Unknown parse result: %d\n", (int)result);
        break;
    }
}

// 处理蓝牙命令
void processBluetoothCommand(const BluetoothFrame &frame)
{
    if (!frame.isValidCommand())
    {
        Serial.println("Error: Invalid command frame");
        return;
    }

    switch (frame.command)
    {
    case BT_CMD_SET_TEXT:
        handleTextCommand(frame);
        break;

    case BT_CMD_SET_COLOR:
        handleColorCommand(frame);
        break;

    case BT_CMD_SET_BRIGHTNESS:
        handleBrightnessCommand(frame);
        break;

    case BT_CMD_SET_EFFECT:
        handleEffectCommand(frame);
        break;

    case BT_CMD_SET_ANIMATION:
        handleAnimationCommand(frame);
        break;

    case BT_CMD_FILE_START:
        handleFileStartCommand(frame);
        break;

    case BT_CMD_FILE_DATA:
        handleFileDataCommand(frame);
        break;

    case BT_CMD_FILE_END:
        handleFileEndCommand(frame);
        break;

    case BT_CMD_FILE_LIST:
        handleFileListCommand(frame);
        break;

    case BT_CMD_FILE_DELETE:
        handleFileDeleteCommand(frame);
        break;

    case BT_CMD_PLAY_GIF:
        handlePlayGifCommand(frame);
        break;

    case BT_CMD_SET_GIF_OFFSET:
        handleGifOffsetCommand(frame);
        break;

    default:
        Serial.printf("Unsupported command: 0x%02X\n", frame.command);
        break;
    }
}

// 处理文本命令
void handleTextCommand(const BluetoothFrame &frame)
{
    String text = frame.getTextData();
    Serial.printf("Set text: %s\n", text.c_str());
    // TODO: 在LED屏上显示文本
}

// 处理颜色命令
void handleColorCommand(const BluetoothFrame &frame)
{
    uint8_t target, mode, r, g, b;
    frame.getColorData(target, mode, r, g, b);
    Serial.printf("Set color - Target: %s, Mode: %s, RGB: (%d,%d,%d)\n",
                  (target == BT_COLOR_TARGET_TEXT) ? "Text" : "Background",
                  (mode == BT_COLOR_MODE_FIXED) ? "Fixed" : "Gradient",
                  r, g, b);
    // TODO: 设置LED屏颜色
}

// 处理亮度命令
void handleBrightnessCommand(const BluetoothFrame &frame)
{
    uint8_t brightness = frame.getBrightnessData();
    Serial.printf("Set brightness: %d (%.1f%%)\n", brightness, brightness * 100.0 / 255.0);
    // TODO: 设置LED屏亮度
}

// 处理特效命令
void handleEffectCommand(const BluetoothFrame &frame)
{
    uint8_t type, speed;
    frame.getEffectData(type, speed);
    const char *effectName = getEffectName(type);
    Serial.printf("Set effect - Type: %s, Speed: %d\n", effectName, speed);
    // TODO: 设置LED屏特效
}

// 处理动画命令
void handleAnimationCommand(const BluetoothFrame &frame)
{
    Serial.printf("Set animation - Data length: %d bytes\n", frame.dataLength);
    // TODO: 处理动画数据
}

// 获取特效名称
const char *getEffectName(uint8_t type)
{
    switch (type)
    {
    case BT_EFFECT_FIXED:
        return "Fixed Display";
    case BT_EFFECT_SCROLL_LEFT:
        return "Scroll Left";
    case BT_EFFECT_SCROLL_RIGHT:
        return "Scroll Right";
    case BT_EFFECT_BLINK:
        return "Blink Effect";
    case BT_EFFECT_BREATHE:
        return "Breathe Effect";
    case BT_EFFECT_SNOW:
        return "Snow Effect";
    case BT_EFFECT_LASER:
        return "Laser Effect";
    default:
        return "Unknown Effect";
    }
}

// LED矩阵屏和GIF相关函数已移动到gif_player.cpp

// 文件传输相关函数已移动到file_handler.cpp

// ==================== 叠层显示测试函数 ====================

//  void testOverlaySystem() {
//      static bool test_executed = false;
//      static unsigned long test_start_time = 0;

//      if (!test_executed && millis() > 15000) { // 15秒后开始测试，避免与启动流程冲突
//          test_executed = true;
//          test_start_time = millis();

//          printf("\n=== Starting Enhanced Overlay System Test ===\n");

//          // 1. 打印系统状态
//          EnhancedTextPixelMask::printSystemStatus();

//         //  // 2. 播放GIF作为背景
//         //  printf("Step 1: Starting GIF background...\n");
//         //  if (playGIF("/gifs/parasite2.gif")) {
//         //      printf("GIF background started successfully\n");
//         //  } else {
//         //      printf("Warning: GIF background failed to start\n");
//         //  }
//          smoothGifSwitch("/gifs/parasite2.gif");
//          // 3. 等待2秒让GIF稳定播放
//          //delay(2000);

//          // 4. 显示时间（应该叠加在GIF上）
//          printf("Step 2: Adding time overlay...\n");
//          setTimeAndWeekdayWithLang(23, 59, 1, LANG_CHINESE);
//          printf("Time overlay added: 12:34 Monday (Chinese)\n");

//         // 5. 等待3秒观察叠层效果
//          delay(3000);

//          // 6. 测试时间变化（文字更新，GIF继续播放）
//          printf("Step 3: Testing time updates...\n");
//          for (int i = 0; i < 5; i++) {
//              setTimeAndWeekdayWithLang(12, 35 + i, 1, LANG_CHINESE);
//              printf("Time updated to: 12:%02d\n", 35 + i);
//              delay(1500);
//          }

//          // 7. 切换到英文显示
//          printf("Step 4: Switching to English display...\n");
//          setTimeAndWeekdayWithLang(23, 59, 1, LANG_ENGLISH);
//          printf("Language switched to English\n");
//          delay(3000);

//          // 8. 切换到大屏模式测试
//         printf("Step 5: Testing big screen mode overlay...\n");
//          setDisplayMode(DISPLAY_MODE_BIG);
//          setBigScreenLanguage(LANG_CHINESE);
//          printf("Switched to big screen mode (Chinese)\n");
//          delay(5000);

//          // 9. 大屏模式语言切换
//          printf("Step 6: Big screen English mode...\n");
//          setBigScreenLanguage(LANG_ENGLISH);
//          delay(3000);

//          // 10. 切换回小屏模式
//          printf("Step 7: Returning to small screen mode...\n");
//          setDisplayMode(DISPLAY_MODE_SMALL);
//          setTimeAndWeekdayWithLang(23, 59, 1, LANG_CHINESE);
//          printf("Switched back to small screen mode\n");
//         delay(3000);

//          // 11. 测试区域信息转储
//          printf("Step 8: Dumping region information...\n");
//          EnhancedTextPixelMask::dumpRegionInfo(16, 0, 48, 16);  // 时间区域
//          EnhancedTextPixelMask::dumpRegionInfo(16, 16, 48, 16); // 星期区域

//          // 12. 打印最终状态
//         // printf("Step 9: Final system status...\n");
//         // EnhancedTextPixelMask::printSystemStatus();

// //         printf("=== Enhanced Overlay System Test Completed ===\n");
// //         printf("Test duration: %lu ms\n", millis() - test_start_time);
// //         printf("Free heap: %d bytes\n", ESP.getFreeHeap());
// //         printf("\nOverlay system is now active. GIF background with text overlay should be visible.\n");
// //         printf("You can now use normal Bluetooth commands to control the display.\n\n");
//      }
//  }
void testOverlaySystem() {
    static bool test_executed = false;
    static unsigned long test_start_time = 0;
    
    if (!test_executed && millis() > 15000) { // 15秒后开始测试，避免与启动流程冲突
        test_executed = true;
        test_start_time = millis();
        
        printf("\n=== Starting Enhanced Overlay System Test ===\n");
        
        // 1. 打印系统状态
        EnhancedTextPixelMask::printSystemStatus();
        
        // // 2. 播放GIF作为背景
        // printf("Step 1: Starting GIF background...\n");
        // if (playGIF("/gifs/parasite2.gif")) {
        //     printf("GIF background started successfully\n");
        // } else {
        //     printf("Warning: GIF background failed to start\n");
        // }
        //smoothGifSwitch("/gifs/parasite2.gif");
        // 3. 等待2秒让GIF稳定播放
        delay(2000);
        
        // 4. 显示时间（应该叠加在GIF上）
        printf("Step 2: Adding time overlay...\n");
        setTimeAndWeekdayWithLang(12, 34, 1, LANG_CHINESE);
        printf("Time overlay added: 12:34 Monday (Chinese)\n");
        
        // 5. 等待3秒观察叠层效果
        delay(3000);
        
        // 6. 测试时间变化（文字更新，GIF继续播放）
        printf("Step 3: Testing time updates...\n");
        for (int i = 0; i < 5; i++) {
            setTimeAndWeekdayWithLang(12, 35 + i, 1, LANG_CHINESE);
            printf("Time updated to: 12:%02d\n", 35 + i);
            delay(1500);
        }
        
        // 7. 切换到英文显示
        printf("Step 4: Switching to English display...\n");
        setTimeAndWeekdayWithLang(12, 40, 1, LANG_ENGLISH);
        printf("Language switched to English\n");
        delay(3000);
        
        // 8. 切换到大屏模式测试
        printf("Step 5: Testing big screen mode overlay...\n");
        setDisplayMode(DISPLAY_MODE_BIG);
        setBigScreenLanguage(LANG_CHINESE);
        printf("Switched to big screen mode (Chinese)\n");
        delay(5000);
        
        // 9. 大屏模式语言切换
        printf("Step 6: Big screen English mode...\n");
        setBigScreenLanguage(LANG_ENGLISH);
        delay(5000);
        
        // 10. 切换回小屏模式
        printf("Step 7: Returning to small screen mode...\n");
        setDisplayMode(DISPLAY_MODE_SMALL);
        setTimeAndWeekdayWithLang(12, 45, 1, LANG_CHINESE);
        printf("Switched back to small screen mode\n");
        delay(3000);
        
        // 11. 测试区域信息转储
        printf("Step 8: Dumping region information...\n");
        EnhancedTextPixelMask::dumpRegionInfo(16, 0, 48, 16);  // 时间区域
        EnhancedTextPixelMask::dumpRegionInfo(16, 16, 48, 16); // 星期区域
        
        // 12. 关闭GIF显示，测试脱离GIF的原有功能
        printf("Step 9: Stopping GIF and testing original functions...\n");
        //stopGIF();  // 关闭GIF显示
        //EnhancedTextPixelMask::clearGifRegion(16,0,63,31,COLOR_BLACK);//清除最后一帧Gif缓存
        printf("GIF stopped. Testing original display functions...\n");
        delay(1000);
        
        // 13. 测试三分区显示（脱离GIF）
        printf("Step 10: Testing three-region display (no GIF)...\n");
        setDisplayMode(DISPLAY_MODE_SMALL);  // 确保是小屏模式
        setTimeAndWeekdayWithLang(13, 30, 2, LANG_CHINESE);  // 周二 13:30
        printf("Three-region display: 13:30 Tuesday (Chinese)\n");
        delay(3000);
        
        // 14. 测试大屏显示（脱离GIF）
        printf("Step 11: Testing big screen display (no GIF)...\n");
        setDisplayMode(DISPLAY_MODE_BIG);
        setBigScreenLanguage(LANG_ENGLISH);
        setBigScreenTimeAndWeekday(13, 35, 2, LANG_ENGLISH);  // 周二 13:35
        printf("Big screen display: 13:35 Tuesday (English)\n");
        delay(3000);
        
        // 15. 打印最终状态
        // printf("Step 12: Final system status...\n");
        // EnhancedTextPixelMask::printSystemStatus();
        
        // printf("=== Enhanced Overlay System Test Completed ===\n");
        // printf("Test duration: %lu ms\n", millis() - test_start_time);
        // printf("Free heap: %d bytes\n", ESP.getFreeHeap());
        // printf("\n=== Original Function Test Results ===\n");
        // printf("✓ Three-region display works without GIF\n");
        // printf("✓ Big screen display works without GIF\n");
        // printf("✓ All original functions preserved\n");
        // printf("\nOverlay system test complete. You can now use normal Bluetooth commands.\n\n");
    }
}
