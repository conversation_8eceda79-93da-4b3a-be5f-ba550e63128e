#include "file_handler.h"
#include "gif_player.h"  // 包含GIF播放器头文件以使用setGifDisplayOffset函数

// 文件传输相关变量定义
bool fileTransferActive = false;
String currentFileName = "";
File currentFile;
uint32_t expectedFileSize = 0;
uint32_t receivedFileSize = 0;
unsigned long fileTransferStartTime = 0;

// 初始化LittleFS文件系统
bool initLittleFS()
{
    if (!LittleFS.begin(FS_FORMAT_ON_FAIL)) {
        Serial.println("LittleFS mount failed");
        return false;
    }

    Serial.println("LittleFS initialized successfully");

    // 列出文件系统中的文件
    File root = LittleFS.open("/");
    File file = root.openNextFile();
    Serial.println("Files in LittleFS:");
    while (file) {
        Serial.printf("  %s (%d bytes)\n", file.name(), file.size());
        file = root.openNextFile();
    }

    return true;
}

// 创建gifs目录
bool createGifsDirectory()
{
    if (!LittleFS.exists(GIF_STORAGE_PATH)) {
        if (LittleFS.mkdir(GIF_STORAGE_PATH)) {
            Serial.println("Created gifs directory successfully");
            return true;
        } else {
            Serial.println("Failed to create gifs directory");
            return false;
        }
    }
    return true;
}

// 发送响应消息
void sendResponse(uint8_t command, const String &message)
{
    // 构造响应帧: 帧头 + 命令 + 长度 + 数据 + 帧尾
    SerialBT.write(BT_FRAME_HEADER_1);
    SerialBT.write(BT_FRAME_HEADER_2);
    SerialBT.write(command | 0x80); // 响应命令 = 原命令 | 0x80

    uint16_t len = message.length();
    SerialBT.write((len >> 8) & 0xFF);
    SerialBT.write(len & 0xFF);

    if (len > 0) {
        SerialBT.write((uint8_t*)message.c_str(), len);
    }

    SerialBT.write(BT_FRAME_TAIL_1);
    SerialBT.write(BT_FRAME_TAIL_2);
}

// 中止文件传输
void abortFileTransfer()
{
    if (fileTransferActive) {
        if (currentFile) {
            currentFile.close();
            // 删除未完成的文件
            LittleFS.remove(currentFileName);
        }
        fileTransferActive = false;
        currentFileName = "";
        expectedFileSize = 0;
        receivedFileSize = 0;
        Serial.println("File transfer aborted");
        sendResponse(BT_CMD_FILE_END, "ERROR:Transfer aborted");
    }
}

// 处理文件开始命令
void handleFileStartCommand(const BluetoothFrame &frame)
{
    String filename;
    uint32_t fileSize;
    frame.getFileStartData(filename, fileSize);

    Serial.printf("Starting file reception: %s, Size: %d bytes\n", filename.c_str(), fileSize);

    // 检查是否已有文件传输在进行
    if (fileTransferActive) {
        Serial.println("Error: File transfer already in progress");
        sendResponse(BT_CMD_FILE_START, "ERROR:Transfer already active");
        return;
    }

    // 检查文件大小
    if (fileSize > FILE_MAX_SIZE) {
        Serial.printf("Error: File too large (%d > %d)\n", fileSize, FILE_MAX_SIZE);
        sendResponse(BT_CMD_FILE_START, "ERROR:File too large");
        return;
    }

    // 检查文件名
    if (filename.length() == 0 || filename.length() > FILE_MAX_NAME_LENGTH) {
        Serial.println("Error: Invalid filename");
        sendResponse(BT_CMD_FILE_START, "ERROR:Invalid filename");
        return;
    }

    // 构造完整文件路径
    currentFileName = GIF_STORAGE_PATH + filename;

    // 创建文件
    currentFile = LittleFS.open(currentFileName, "w");
    if (!currentFile) {
        Serial.printf("Error: Cannot create file %s\n", currentFileName.c_str());
        sendResponse(BT_CMD_FILE_START, "ERROR:Cannot create file");
        return;
    }

    // 初始化传输状态
    fileTransferActive = true;
    expectedFileSize = fileSize;
    receivedFileSize = 0;
    fileTransferStartTime = millis();

    Serial.println("File transfer started");
    sendResponse(BT_CMD_FILE_START, "OK:Ready to receive");
}

// 处理文件数据命令
void handleFileDataCommand(const BluetoothFrame &frame)
{
    if (!fileTransferActive) {
        Serial.println("Error: No active file transfer");
        sendResponse(BT_CMD_FILE_DATA, "ERROR:No active transfer");
        return;
    }

    if (!currentFile) {
        Serial.println("Error: File not open");
        abortFileTransfer();
        return;
    }

    // 检查数据长度
    if (frame.dataLength == 0) {
        Serial.println("Error: Data length is 0");
        sendResponse(BT_CMD_FILE_DATA, "ERROR:Empty data");
        return;
    }

    // 检查是否超出预期大小
    if (receivedFileSize + frame.dataLength > expectedFileSize) {
        Serial.printf("Error: Data exceeds expected size (%d + %d > %d)\n",
                     receivedFileSize, frame.dataLength, expectedFileSize);
        abortFileTransfer();
        return;
    }

    // 写入数据
    size_t written = currentFile.write(frame.data, frame.dataLength);
    if (written != frame.dataLength) {
        Serial.printf("Error: Write failed (written %d, expected %d)\n", written, frame.dataLength);
        abortFileTransfer();
        return;
    }

    receivedFileSize += frame.dataLength;
    fileTransferStartTime = millis(); // 更新超时时间

    Serial.printf("Received data: %d/%d bytes (%.1f%%)\n",
                 receivedFileSize, expectedFileSize,
                 (float)receivedFileSize * 100.0 / expectedFileSize);

    // 检查蓝牙缓冲区状态，如果有积压数据先处理
    if (SerialBT.available() > 512) {
        Serial.printf("Warning: Bluetooth buffer backlog %d bytes, suggest slowing down transmission\n", SerialBT.available());
    }

    sendResponse(BT_CMD_FILE_DATA, "OK:Data received");
}

// 处理文件结束命令
void handleFileEndCommand(const BluetoothFrame &frame)
{
    if (!fileTransferActive) {
        Serial.println("Error: No active file transfer");
        sendResponse(BT_CMD_FILE_END, "ERROR:No active transfer");
        return;
    }

    // 检查文件大小是否匹配
    if (receivedFileSize != expectedFileSize) {
        Serial.printf("Error: File size mismatch (received %d, expected %d)\n",
                     receivedFileSize, expectedFileSize);
        abortFileTransfer();
        return;
    }

    // 关闭文件
    currentFile.close();

    Serial.printf("File transfer completed: %s (%d bytes)\n", currentFileName.c_str(), receivedFileSize);

    // 重置传输状态
    fileTransferActive = false;
    String savedFileName = currentFileName;
    currentFileName = "";
    expectedFileSize = 0;
    receivedFileSize = 0;

    sendResponse(BT_CMD_FILE_END, "OK:File saved successfully");

    // 如果是GIF文件，可以自动播放
    if (savedFileName.endsWith(".gif")) {
        Serial.println("GIF file detected, can be played using play command");
    }
}

// 处理文件列表命令
void handleFileListCommand(const BluetoothFrame &frame)
{
    Serial.println("Getting file list");
    sendFileList();
}

// 发送文件列表
void sendFileList()
{
    String fileList = "";
    File root = LittleFS.open(GIF_STORAGE_PATH);

    if (!root) {
        sendResponse(BT_CMD_FILE_LIST, "ERROR:Cannot open directory");
        return;
    }

    File file = root.openNextFile();
    while (file) {
        if (!file.isDirectory()) {
            if (fileList.length() > 0) {
                fileList += ";";
            }
            fileList += file.name();
            fileList += ":";
            fileList += String(file.size());
        }
        file = root.openNextFile();
    }

    if (fileList.length() == 0) {
        fileList = "No files found";
    }

    Serial.printf("File list: %s\n", fileList.c_str());
    sendResponse(BT_CMD_FILE_LIST, fileList);
}

// 处理删除文件命令
void handleFileDeleteCommand(const BluetoothFrame &frame)
{
    String filename = frame.getFileDeleteData();

    if (filename.length() == 0) {
        Serial.println("Error: Empty filename");
        sendResponse(BT_CMD_FILE_DELETE, "ERROR:Empty filename");
        return;
    }

    String fullPath = GIF_STORAGE_PATH + filename;

    if (LittleFS.exists(fullPath)) {
        if (LittleFS.remove(fullPath)) {
            Serial.printf("File deleted successfully: %s\n", filename.c_str());
            sendResponse(BT_CMD_FILE_DELETE, "OK:File deleted");
        } else {
            Serial.printf("File deletion failed: %s\n", filename.c_str());
            sendResponse(BT_CMD_FILE_DELETE, "ERROR:Delete failed");
        }
    } else {
        Serial.printf("File not found: %s\n", filename.c_str());
        sendResponse(BT_CMD_FILE_DELETE, "ERROR:File not found");
    }
}

// 处理播放GIF命令
void handlePlayGifCommand(const BluetoothFrame &frame)
{
    String filename = frame.getPlayGifData();

    if (filename.length() == 0) {
        Serial.println("Error: Empty filename");
        sendResponse(BT_CMD_PLAY_GIF, "ERROR:Empty filename");
        return;
    }

    String fullPath = GIF_STORAGE_PATH + filename;

    if (!LittleFS.exists(fullPath)) {
        Serial.printf("File not found: %s\n", filename.c_str());
        sendResponse(BT_CMD_PLAY_GIF, "ERROR:File not found");
        return;
    }

    // 调用gif_player模块的playGIF函数

    if (playGIF(fullPath.c_str())) {
        Serial.printf("Started playing GIF: %s\n", filename.c_str());
        sendResponse(BT_CMD_PLAY_GIF, "OK:Playing GIF");
    } else {
        Serial.printf("Failed to play GIF: %s\n", filename.c_str());
        sendResponse(BT_CMD_PLAY_GIF, "ERROR:Play failed");
    }
}

// 处理GIF偏移量命令
void handleGifOffsetCommand(const BluetoothFrame &frame)
{
    int16_t offset_x, offset_y;
    frame.getGifOffsetData(offset_x, offset_y);

    Serial.printf("Received GIF offset command: (%d, %d)\n", offset_x, offset_y);

    // 调用gif_player模块的setGifDisplayOffset函数
    setGifDisplayOffset(offset_x, offset_y);

    sendResponse(BT_CMD_SET_GIF_OFFSET, "OK:Offset set");
}
