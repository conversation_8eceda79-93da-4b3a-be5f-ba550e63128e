# 大屏模式功能使用说明

## 功能概述

系统现已支持两种显示模式：
- **小屏模式（默认）**：原有的三分区显示（GIF + 时间 + 星期）
- **大屏模式（新增）**：右侧48x32大区域显示（前5秒显示时间，后续显示星期）

## 核心特性

### ✅ **完全兼容**
- 保持所有现有功能不变
- 默认小屏模式，现有代码无需修改
- 新旧功能可以无缝切换

### ✅ **智能切换**
- 大屏模式下自动在时间和星期之间切换
- 可配置切换间隔（默认5秒）
- 支持中英文星期显示

### ✅ **独立颜色控制**
- 时间颜色独立控制
- 星期颜色独立控制  
- 背景颜色独立控制

### ✅ **真实字体数据**
- 32x8数字字体（时间显示）
- 32x32中文字体（星期显示）
- 32x16英文字体（星期显示）

## API接口

### **模式切换函数**

#### 1. 基本模式切换
```cpp
// 切换到大屏模式
setDisplayMode(DISPLAY_MODE_BIG);

// 切换到小屏模式  
setDisplayMode(DISPLAY_MODE_SMALL);
```

#### 2. 一键切换（推荐）
```cpp
// 一键切换到大屏模式并设置所有参数
switchToBigScreen(hour, minute, weekday, language, switch_interval, time_color, weekday_color);

// 示例：14:30 周六 英文显示 3秒切换 红色时间 蓝色星期
switchToBigScreen(14, 30, 6, LANG_ENGLISH, 3000, COLOR_RED, COLOR_BLUE);

// 一键切换回小屏模式
switchToSmallScreen();
```

### **配置函数**

#### 1. 时间和星期设置
```cpp
// 设置大屏时间和星期
setBigScreenTimeAndWeekday(23, 59, 6, LANG_ENGLISH);  // 23:59 周六 英文
```

#### 2. 切换间隔设置
```cpp
// 设置切换间隔（毫秒）
setBigScreenSwitchInterval(5000);  // 5秒切换
setBigScreenSwitchInterval(3000);  // 3秒切换
setBigScreenSwitchInterval(10000); // 10秒切换
```

#### 3. 语言设置
```cpp
// 设置大屏星期显示语言
setBigScreenLanguage(LANG_CHINESE);  // 中文：星期六
setBigScreenLanguage(LANG_ENGLISH);  // 英文：SAT
```

### **颜色控制函数**

#### 1. 单独颜色设置
```cpp
// 设置时间颜色
setBigScreenTimeColor(COLOR_RED);     // 红色时间
setBigScreenTimeColor(COLOR_GREEN);   // 绿色时间

// 设置星期颜色
setBigScreenWeekdayColor(COLOR_BLUE); // 蓝色星期
setBigScreenWeekdayColor(COLOR_YELLOW); // 黄色星期

// 设置背景颜色
setBigScreenBackgroundColor(COLOR_BLACK); // 黑色背景
```

#### 2. 颜色主题设置
```cpp
// 一键设置颜色主题
setBigScreenColorTheme(COLOR_RED, COLOR_BLUE, COLOR_BLACK);
// 参数：时间颜色, 星期颜色, 背景颜色
```

## 使用示例

### **示例1：基本使用**
```cpp
void setup() {
    // 系统初始化...
    
    // 切换到大屏模式
    switchToBigScreen(12, 30, 1, LANG_ENGLISH, 5000, COLOR_RED, COLOR_BLUE);
    // 12:30 周一 英文显示 5秒切换 红色时间 蓝色星期
}

void loop() {
    // 系统会自动处理大屏显示更新
    updateGIF();
    
    if (current_display_mode == DISPLAY_MODE_SMALL) {
        updateTimeDisplay();
    } else {
        updateBigScreenDisplay();  // 自动处理时间/星期切换
    }
    
    updateAllRegions();
}
```

### **示例2：动态切换**
```cpp
void loop() {
    static unsigned long last_mode_switch = 0;
    static bool is_big_mode = false;
    
    // 每30秒切换一次显示模式
    if (millis() - last_mode_switch > 30000) {
        if (is_big_mode) {
            switchToSmallScreen();
        } else {
            switchToBigScreen(14, 30, 2, LANG_CHINESE, 3000, COLOR_GREEN, COLOR_YELLOW);
        }
        is_big_mode = !is_big_mode;
        last_mode_switch = millis();
    }
    
    // 正常更新逻辑...
}
```

### **示例3：命令控制**
```cpp
// 模拟蓝牙命令处理
void handleDisplayCommand(const char* command) {
    if (strcmp(command, "big") == 0) {
        // 收到"big"命令，切换到大屏模式
        switchToBigScreen(time_info.hour, time_info.minute, time_info.weekday, 
                         LANG_ENGLISH, 5000, COLOR_RED, COLOR_BLUE);
        printf("Switched to big screen mode\n");
        
    } else if (strcmp(command, "small") == 0) {
        // 收到"small"命令，切换到小屏模式
        switchToSmallScreen();
        printf("Switched to small screen mode\n");
    }
}
```

## 显示效果

### **小屏模式（原有）**
```
┌─────────────────┬─────────────────────────────────┐
│                 │            12:30                │
│   GIF动画       │         (48x16区域)             │
│   (16x32)       │                                 │
│                 ├─────────────────────────────────┤
│                 │           星期一                │
│                 │         (48x16区域)             │
└─────────────────┴─────────────────────────────────┘
```

### **大屏模式（新增）**
```
┌─────────────────┬─────────────────────────────────┐
│                 │                                 │
│   GIF动画       │          12:30                  │
│   (16x32)       │      (32x8大字体)               │
│                 │        (48x32区域)              │
│                 │                                 │
└─────────────────┴─────────────────────────────────┘

5秒后自动切换为：

┌─────────────────┬─────────────────────────────────┐
│                 │                                 │
│   GIF动画       │         星期一                  │
│   (16x32)       │     (32x32大字体)               │
│                 │        (48x32区域)              │
│                 │                                 │
└─────────────────┴─────────────────────────────────┘
```

## 测试功能

### **自动测试**
系统启动10秒后会自动运行完整测试：
```cpp
testBigScreenMode();  // 完全独立的测试函数
```

### **测试内容**
1. ✅ 基本功能检查
2. ✅ 切换到大屏模式
3. ✅ 时间显示测试（3秒）
4. ✅ 星期显示切换测试
5. ✅ 中文显示测试
6. ✅ 颜色变化测试
7. ✅ 小屏模式切换测试
8. ✅ 状态保持验证

### **调试输出**
所有操作都有详细的printf调试输出：
```
=== Switching to Big Screen Mode ===
Big screen time and weekday set: 14:30, weekday=2, lang=1
Big screen switch interval set to: 3000 ms
Big screen time color set to: 0xF800
Big screen weekday color set to: 0x001F
Display mode changed to BIG
Big screen mode activated successfully
```

## 技术特点

### **内存优化**
- 使用真实点阵数据，无需动态生成
- 智能缩放算法，适配48像素宽度限制
- 完整的内存管理和边界检查

### **性能优化**
- 只在状态变化时重新绘制
- 高效的像素级绘制算法
- 最小化主循环开销

### **兼容性保证**
- 所有现有函数完全不变
- 默认小屏模式，向后兼容
- 新旧功能可以混合使用

## 总结

大屏模式功能已完全集成到现有系统中，提供了：

1. **完整的API接口**：从基本切换到高级配置
2. **真实字体支持**：基于您提供的点阵数据
3. **智能显示逻辑**：自动时间/星期切换
4. **独立颜色控制**：时间、星期、背景分别控制
5. **完全兼容性**：不影响任何现有功能
6. **完整测试**：自动测试验证所有功能

您现在可以通过简单的函数调用在两种显示模式之间自由切换，享受大屏显示的视觉效果！
