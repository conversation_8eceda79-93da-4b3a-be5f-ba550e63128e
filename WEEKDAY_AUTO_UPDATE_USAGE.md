# 星期自动更新功能使用说明

## 功能概述

已成功添加星期自动更新逻辑，实现了时间跨天时星期的自动切换功能。

## 核心功能

### 自动星期更新
- **触发条件**：当时间从 23:59 更新到 00:00 时
- **更新逻辑**：星期自动递增，周六(6)后回到周日(0)
- **显示更新**：星期区域自动刷新显示新的星期

### 星期循环规律
```
周日(0) → 周一(1) → 周二(2) → 周三(3) → 周四(4) → 周五(5) → 周六(6) → 周日(0) ...
```

## 实现逻辑

### 时间更新流程
```cpp
// 每分钟检查一次
if (time_info.minute >= 60) {
    time_info.minute = 0;
    time_info.hour++;
    
    if (time_info.hour >= 24) {
        time_info.hour = 0;
        // 新的一天开始，星期自动递增
        time_info.weekday++;
        if (time_info.weekday > 6) {
            time_info.weekday = 0;  // 周日重新开始
        }
    }
}
```

### 显示更新机制
- **时间显示**：当小时或分钟变化时更新
- **星期显示**：当星期数值变化时更新
- **串口日志**：记录时间和星期的变化

## 新增接口函数

### 1. 同时设置时间和星期
```cpp
// 设置为周一 15:30
setTimeAndWeekday(15, 30, 1);

// 设置为周五 23:58 (测试跨天功能)
setTimeAndWeekday(23, 58, 5);
```

### 2. 获取星期名称
```cpp
const char* name = getWeekdayName(1);  // 返回 "一"
Serial.printf("今天是星期%s\n", name);
```

## 使用示例

### 基本设置
```cpp
// 在 setup() 或任何地方调用
setTimeAndWeekday(13, 12, 1);  // 周一 13:12
```

### 测试跨天功能
```cpp
// 设置为周一 23:59，等待自动切换到周二 00:00
setTimeAndWeekday(23, 59, 1);

// 一分钟后会自动变为：
// 时间：00:00
// 星期：周二(2)
```

### 测试周末到周一
```cpp
// 设置为周六 23:59，等待自动切换到周日 00:00
setTimeAndWeekday(23, 59, 6);

// 一分钟后会自动变为：
// 时间：00:00
// 星期：周日(0)
```

## 串口日志输出

### 正常时间更新
```
Time updated: 13:13
Time updated: 13:14
...
```

### 跨天更新
```
Time updated: 00:00
New day! Weekday changed to: 2
Weekday updated: 星期二
```

### 设置时间和星期
```
Time and weekday set to: 15:30 星期一
```

## 显示效果演示

### 周一 23:59
```
┌─────────┬─────────────────────────┐
│         │      23:59 (红色)       │
│   GIF   ├─────────────────────────┤
│ REGION  │   星期一 (蓝色)          │
│         │                         │
└─────────┴─────────────────────────┘
```

### 一分钟后自动变为周二 00:00
```
┌─────────┬─────────────────────────┐
│         │      00:00 (红色)       │
│   GIF   ├─────────────────────────┤
│ REGION  │   星期二 (蓝色)          │
│         │                         │
└─────────┴─────────────────────────┘
```

## 技术特点

### 1. 智能更新检测
- 使用静态变量记录上次显示的值
- 只在数值真正变化时才更新显示
- 避免不必要的重绘，提高性能

### 2. 完整的状态管理
- 时间状态：小时、分钟
- 星期状态：0-6 对应周日到周六
- 显示状态：独立跟踪每个区域的更新需求

### 3. 详细的日志记录
- 时间变化日志
- 星期变化日志
- 设置操作日志

## 兼容性保证

- ✅ **完全不影响GIF播放功能**
- ✅ **不影响现有的时间设置功能**
- ✅ **保持所有蓝牙协议功能**
- ✅ **向后兼容所有现有接口**

## 扩展建议

### 1. 月份和日期支持
```cpp
// 未来可以扩展
typedef struct {
    uint8_t year;
    uint8_t month;
    uint8_t day;
    uint8_t hour;
    uint8_t minute;
    uint8_t weekday;
} FullDateTime;
```

### 2. 节假日支持
```cpp
// 可以添加特殊日期的显示
bool isHoliday(uint8_t month, uint8_t day);
void drawHolidayToRegion(DisplayRegion* region, const char* holiday_name);
```

### 3. 闰年处理
```cpp
// 处理2月29日等特殊情况
bool isLeapYear(uint16_t year);
uint8_t getDaysInMonth(uint8_t month, uint16_t year);
```

现在你的时钟系统具备了完整的星期自动更新功能！
