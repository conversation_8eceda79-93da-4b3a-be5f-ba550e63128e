# 多语言星期显示使用示例

## 功能概述

系统现已支持中英文星期显示切换，您可以在运行时动态选择显示语言。

## 新增的API接口

### 1. 语言枚举
```cpp
typedef enum {
    LANG_CHINESE = 0,  // 中文：显示"星期一"
    LANG_ENGLISH = 1,  // 英文：显示"MON"
    LANG_COUNT
} WeekdayLanguage;
```

### 2. 多语言函数接口
```cpp
// 设置星期并指定语言
void setWeekdayWithLanguage(uint8_t weekday, WeekdayLanguage language);

// 同时设置时间和星期并指定语言 (新增)
void setTimeAndWeekdayWithLang(uint8_t hour, uint8_t minute, uint8_t weekday, WeekdayLanguage language);

// 获取星期名称（支持多语言）
const char* getWeekdayNameWithLang(uint8_t weekday, WeekdayLanguage language);

// 绘制星期到指定区域（支持多语言）
void drawWeekdayToRegionWithLang(DisplayRegion* region, uint8_t weekday, WeekdayLanguage language);
```

## 使用示例

### 基本使用
```cpp
void setup() {
    // 初始化系统
    initThreeRegionClock();

    // 方式1：分别设置时间和星期
    setTime(23, 59);                          // 设置时间为23:59
    setWeekdayWithLanguage(1, LANG_CHINESE);  // 设置中文星期一

    delay(3000);

    // 方式2：同时设置时间和星期（推荐）
    setTimeAndWeekdayWithLang(23, 59, 1, LANG_ENGLISH);  // 23:59 MON

    delay(3000);

    // 方式3：使用原有函数（向后兼容）
    setTimeAndWeekday(23, 59, 0);  // 23:59 星期日（默认中文）
}
```

### 动态语言切换演示
```cpp
void loop() {
    static unsigned long last_switch = 0;
    static WeekdayLanguage current_lang = LANG_CHINESE;
    static uint8_t current_weekday = 0;
    static uint8_t current_hour = 12;
    static uint8_t current_minute = 0;

    // 每5秒切换一次语言和时间
    if (millis() - last_switch > 5000) {
        // 循环显示所有星期
        current_weekday = (current_weekday + 1) % 7;

        // 时间递增
        current_minute += 15;
        if (current_minute >= 60) {
            current_minute = 0;
            current_hour = (current_hour + 1) % 24;
        }

        // 每7次切换一次语言
        if (current_weekday == 0) {
            current_lang = (current_lang == LANG_CHINESE) ? LANG_ENGLISH : LANG_CHINESE;
        }

        // 使用新函数同时设置时间和星期
        setTimeAndWeekdayWithLang(current_hour, current_minute, current_weekday, current_lang);
        last_switch = millis();
    }

    updateThreeRegionClock();
}
```

### 蓝牙控制语言切换
```cpp
// 在蓝牙命令处理中添加时间和语言设置
void handleTimeLanguageCommand(const BluetoothFrame &frame) {
    if (frame.dataLength >= 4) {
        uint8_t hour = frame.data[0];
        uint8_t minute = frame.data[1];
        uint8_t weekday = frame.data[2];
        uint8_t language = frame.data[3];

        if (hour < 24 && minute < 60 && weekday <= 6 && language < LANG_COUNT) {
            // 使用新函数同时设置时间、星期和语言
            setTimeAndWeekdayWithLang(hour, minute, weekday, (WeekdayLanguage)language);
            Serial.printf("Time and language set: %02d:%02d, weekday=%d, lang=%d\n",
                         hour, minute, weekday, language);
        }
    }
}

// 兼容原有的单独语言切换
void handleLanguageCommand(const BluetoothFrame &frame) {
    if (frame.dataLength >= 2) {
        uint8_t weekday = frame.data[0];
        uint8_t language = frame.data[1];

        if (weekday <= 6 && language < LANG_COUNT) {
            setWeekdayWithLanguage(weekday, (WeekdayLanguage)language);
            Serial.printf("Language switched: weekday=%d, lang=%d\n", weekday, language);
        }
    }
}
```

## 显示效果对比

### 中文显示 (LANG_CHINESE)
```
┌─────────────────────────────────┐
│ 星期一                          │
│ 星期二                          │
│ 星期三                          │
│ 星期四                          │
│ 星期五                          │
│ 星期六                          │
│ 星期日                          │
└─────────────────────────────────┘
```

### 英文显示 (LANG_ENGLISH)
```
┌─────────────────────────────────┐
│        MON                      │
│        TUE                      │
│        WED                      │
│        THU                      │
│        FRI                      │
│        SAT                      │
│        SUN                      │
└─────────────────────────────────┘
```

## 兼容性说明

### 向后兼容
- 原有的`setWeekday()`函数仍然可用，默认使用中文显示
- 原有的`setTimeAndWeekday()`函数保持不变，默认使用中文显示
- 原有的`drawWeekdayToRegion()`函数保持不变
- 所有现有代码无需修改即可正常工作

### 函数对比表

| 功能 | 原有函数 | 新增函数 | 说明 |
|------|----------|----------|------|
| 设置时间 | `setTime(hour, minute)` | 无变化 | 仅设置时间，不影响星期显示 |
| 设置星期 | `setWeekday(weekday)` | `setWeekdayWithLanguage(weekday, lang)` | 新函数支持语言选择 |
| 设置时间+星期 | `setTimeAndWeekday(h, m, w)` | `setTimeAndWeekdayWithLang(h, m, w, lang)` | 新函数支持语言选择 |
| 获取星期名 | `getWeekdayName(weekday)` | `getWeekdayNameWithLang(weekday, lang)` | 新函数支持多语言 |
| 绘制星期 | `drawWeekdayToRegion(region, weekday)` | `drawWeekdayToRegionWithLang(region, weekday, lang)` | 新函数支持多语言 |

### 使用建议

1. **新项目**：推荐使用带语言参数的新函数，获得完整的多语言支持
2. **现有项目**：可以继续使用原有函数，需要多语言时再切换到新函数
3. **混合使用**：新旧函数可以在同一项目中混合使用，互不冲突

### 新功能特性
- 英文星期显示自动居中对齐
- 支持运行时动态语言切换
- 完整的调试信息输出
- 统一的错误处理机制

## 调试信息

系统会输出详细的调试信息：

```
// 原有函数（中文）
Time and weekday set to: 23:59 星期日

// 新函数（中文）
Time and weekday set to: 23:59 星期一

// 新函数（英文）
Time and weekday set to: 23:59 MON

// 单独设置星期（中文）
Weekday set to: 星期一

// 单独设置星期（英文）
Weekday set to: MON
```

## 扩展建议

基于当前的架构，您可以轻松添加更多语言：

1. **扩展语言枚举**：在`WeekdayLanguage`中添加新语言
2. **添加字体数据**：为新语言创建字体数据数组
3. **扩展绘制函数**：在`drawWeekdayToRegionWithLang`中添加新语言分支
4. **更新名称映射**：在`getWeekdayNameWithLang`中添加新语言的名称

这套多语言系统为您的项目提供了强大的国际化支持基础。
