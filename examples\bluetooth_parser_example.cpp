#include "bluetooth_protocol.h"

BluetoothProtocolParser parser;
BluetoothFrame frame;

void setup() {
    Serial.begin(115200);
    Serial.println("Bluetooth Protocol Parser Example");
}

void loop() {
    // 模拟接收蓝牙数据
    if (Serial.available()) {
        uint8_t receivedByte = Serial.read();
        ParseResult result = parser.parseByte(receivedByte, frame);
        
        switch (result) {
            case ParseResult::FRAME_COMPLETE:
                handleCompleteFrame(frame);
                break;
                
            case ParseResult::FRAME_ERROR:
                Serial.println("Frame error");
                parser.printDebugInfo();
                break;

            case ParseResult::INVALID_COMMAND:
                Serial.println("Invalid command");
                break;

            case ParseResult::DATA_TOO_LONG:
                Serial.println("Data too long");
                break;
                
            case ParseResult::NEED_MORE_DATA:
                // 继续等待更多数据
                break;
        }
    }
    
    // 检查超时
    if (parser.isFrameTimeout()) {
        Serial.println("Frame receive timeout");
        parser.reset();
    }
}

void handleCompleteFrame(const BluetoothFrame& frame) {
    Serial.printf("Received complete frame - Command: 0x%02X, Data length: %d\n",
                  frame.command, frame.dataLength);
    
    switch (frame.command) {
        case BT_CMD_SET_TEXT:
            handleTextCommand(frame);
            break;
            
        case BT_CMD_SET_COLOR:
            handleColorCommand(frame);
            break;
            
        case BT_CMD_SET_BRIGHTNESS:
            handleBrightnessCommand(frame);
            break;
            
        case BT_CMD_SET_EFFECT:
            handleEffectCommand(frame);
            break;
            
        default:
            Serial.println("Unknown command");
            break;
    }
}

void handleTextCommand(const BluetoothFrame& frame) {
    String text = frame.getTextData();
    Serial.printf("Set text: %s\n", text.c_str());
}

void handleColorCommand(const BluetoothFrame& frame) {
    uint8_t target, mode, r, g, b;
    frame.getColorData(target, mode, r, g, b);
    Serial.printf("Set color - Target: %s, Mode: %s, RGB: (%d,%d,%d)\n",
                  (target == BT_COLOR_TARGET_TEXT) ? "Text" : "Background",
                  (mode == BT_COLOR_MODE_FIXED) ? "Fixed" : "Gradient",
                  r, g, b);
}

void handleBrightnessCommand(const BluetoothFrame& frame) {
    uint8_t brightness = frame.getBrightnessData();
    Serial.printf("Set brightness: %d\n", brightness);
}

void handleEffectCommand(const BluetoothFrame& frame) {
    uint8_t type, speed;
    frame.getEffectData(type, speed);
    Serial.printf("Set effect - Type: %d, Speed: %d\n", type, speed);
}

// 批量解析示例
void batchParseExample() {
    uint8_t buffer[] = {
        0xAA, 0x55, 0x01, 0x00, 0x05, 'H', 'e', 'l', 'l', 'o', 0x0D, 0x0A, // 文本命令
        0xAA, 0x55, 0x04, 0x00, 0x01, 0x80, 0x0D, 0x0A                      // 亮度命令
    };
    
    BluetoothFrame frames[10];
    size_t frameCount;
    
    ParseResult result = parser.parseBuffer(buffer, sizeof(buffer), frames, 10, frameCount);
    
    Serial.printf("Parsed %d complete frames\n", frameCount);
    for (size_t i = 0; i < frameCount; i++) {
        handleCompleteFrame(frames[i]);
    }
}
