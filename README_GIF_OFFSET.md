# GIF显示偏移量功能说明

## 功能概述

本功能允许您设置GIF动画在LED矩阵屏上的显示位置，通过设置X、Y偏移量来控制GIF的显示基准点，实现居中显示、偏移显示等各种效果。

## 主要特性

- ✅ **灵活定位**：支持任意X、Y坐标偏移
- ✅ **边界保护**：自动进行边界检查，防止越界显示
- ✅ **动态调整**：运行时可随时修改偏移量
- ✅ **向后兼容**：默认偏移量为(0,0)，保持原有行为
- ✅ **蓝牙控制**：支持通过蓝牙协议远程设置
- ✅ **负值支持**：支持负偏移量，实现部分显示效果

## API接口

### 核心函数

```cpp
// 设置GIF显示偏移量
void setGifDisplayOffset(int offset_x, int offset_y);

// 重置偏移量为默认值
void resetGifDisplayOffset();

// 获取当前偏移量
void getGifDisplayOffset(int* offset_x, int* offset_y);
```

### 使用示例

#### 基本使用
```cpp
// 设置GIF显示在屏幕中央（假设GIF是32x16，屏幕是64x32）
setGifDisplayOffset(16, 8);

// 播放GIF
playGIF("/gifs/kaiji.gif");

// 重置为左上角
resetGifDisplayOffset();
```

#### 动态调整
```cpp
// 获取当前偏移量
int current_x, current_y;
getGifDisplayOffset(&current_x, &current_y);

// 向右移动5像素
setGifDisplayOffset(current_x + 5, current_y);
```

## 蓝牙协议

### 命令格式

- **命令码**：`BT_CMD_SET_GIF_OFFSET` (0x16)
- **数据长度**：4字节
- **数据格式**：
  - 字节0-1：X偏移量（16位有符号整数，大端序）
  - 字节2-3：Y偏移量（16位有符号整数，大端序）

### 协议示例

#### 设置偏移量为(10, 5)
```
发送：AA 55 16 04 00 0A 00 05 0D 0A
响应：OK:Offset set
```

#### 设置偏移量为(-5, -3)
```
发送：AA 55 16 04 FF FB FF FD 0D 0A
响应：OK:Offset set
```

## 配置参数

在 `config.h` 中的相关配置：

```cpp
// GIF显示位置配置
#define GIF_DEFAULT_OFFSET_X 0      // 默认X偏移量
#define GIF_DEFAULT_OFFSET_Y 0      // 默认Y偏移量

// 蓝牙命令
#define BT_CMD_SET_GIF_OFFSET 0x16  // 设置偏移量命令
```

## 实现原理

### 坐标变换

原始绘制坐标：
```cpp
x = pDraw->iX + pixel_x
y = pDraw->iY + pDraw->y
```

应用偏移后：
```cpp
actual_x = pDraw->iX + pixel_x + gif_offset_x
actual_y = pDraw->iY + pDraw->y + gif_offset_y
```

### 边界检查

```cpp
// X坐标边界检查
if (actual_x >= 0 && actual_x < SCREEN_WIDTH) {
    // 绘制像素
}

// Y坐标边界检查
if (actual_y >= 0 && actual_y < SCREEN_HEIGHT) {
    // 继续处理该行
}
```

## 常用场景

### 1. 居中显示
```cpp
// 对于32x16的GIF在64x32屏幕上居中
setGifDisplayOffset(16, 8);
```

### 2. 右下角显示
```cpp
// GIF显示在右下角
setGifDisplayOffset(32, 16);  // 根据GIF尺寸调整
```

### 3. 部分显示（裁剪效果）
```cpp
// 只显示GIF的右半部分
setGifDisplayOffset(-16, 0);
```

### 4. 动画移动效果
```cpp
// 从左到右移动
for (int i = 0; i < 32; i++) {
    setGifDisplayOffset(i, 0);
    delay(100);
}
```

## 注意事项

1. **偏移量范围**：系统会自动将偏移量限制在 `[-SCREEN_WIDTH, SCREEN_WIDTH]` 和 `[-SCREEN_HEIGHT, SCREEN_HEIGHT]` 范围内

2. **性能影响**：边界检查会在每个像素绘制时进行，但性能影响微乎其微

3. **兼容性**：该功能完全向后兼容，不会影响现有代码

4. **内存使用**：仅增加两个int变量，内存占用极小

## 故障排除

### 问题：GIF不显示
- 检查偏移量是否过大，导致GIF完全移出屏幕
- 使用 `getGifDisplayOffset()` 查看当前偏移量

### 问题：显示位置不正确
- 确认GIF的实际尺寸
- 计算正确的偏移量：`offset = (screen_size - gif_size) / 2`

### 问题：蓝牙命令无响应
- 检查数据长度是否为4字节
- 确认字节序是否正确（大端序）

## 更新日志

- **v1.0**：初始版本，支持基本偏移功能
- **v1.1**：添加蓝牙协议支持
- **v1.2**：增加边界检查和负偏移支持
