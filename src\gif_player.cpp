#include "gif_player.h"

// ==================== EnhancedTextPixelMask静态成员定义 ====================
bool EnhancedTextPixelMask::system_initialized = false;
bool* EnhancedTextPixelMask::text_mask = nullptr;
uint16_t* EnhancedTextPixelMask::gif_backup = nullptr;
uint16_t EnhancedTextPixelMask::default_bg_color = COLOR_BLACK;

// GIF播放相关变量定义
MatrixPanel_I2S_DMA *dma_display = nullptr;
AnimatedGIF gif;
File gifFile;
bool gifPlaying = false;
unsigned long lastGifFrame = 0;
int gifFrameDelay = GIF_DEFAULT_FRAME_DELAY; // 默认帧延迟
int gif_offset_x = GIF_DEFAULT_OFFSET_X;  // GIF显示X偏移量，默认为0
int gif_offset_y = GIF_DEFAULT_OFFSET_Y;  // GIF显示Y偏移量，默认为0

// 内存管理相关变量定义
uint8_t* currentGifData = nullptr;  // 跟踪当前malloc的GIF数据
size_t currentGifDataSize = 0;      // 跟踪当前GIF数据大小

// 全局语言状态管理
WeekdayLanguage current_display_language = LANG_CHINESE;  // 默认中文，保持向后兼容

// 大屏模式全局状态管理
DisplayMode current_display_mode = DISPLAY_MODE_SMALL;  // 默认小屏模式，保持向后兼容
BigScreenState big_screen_state = BIG_SCREEN_TIME;
unsigned long big_screen_last_switch = 0;
unsigned long big_screen_switch_interval = 5000;  // 默认5秒切换
WeekdayLanguage big_screen_language = LANG_ENGLISH;
uint16_t big_screen_time_color = COLOR_RED;
uint16_t big_screen_weekday_color = COLOR_BLUE;
uint16_t big_screen_bg_color = COLOR_BLACK;

// 字体数据 (数字0-9和冒号)
const unsigned char font_data[11][16] = {
    {  // 字符":"
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x04, 0x04,
        0x00, 0x00, 0x00, 0x00, 0x04, 0x04, 0x00, 0x00
    },
    {  // 字符 '0'
        0x00, 0x00, 0x00, 0x18, 0x24, 0x42, 0x42, 0x42,
        0x42, 0x42, 0x42, 0x42, 0x24, 0x18, 0x00, 0x00
    },
    {  // 字符 '1'
        0x00, 0x00, 0x00, 0x10, 0x1C, 0x10, 0x10, 0x10,
        0x10, 0x10, 0x10, 0x10, 0x10, 0x7C, 0x00, 0x00
    },
    {  // 字符 '2'
        0x00, 0x00, 0x00, 0x3C, 0x42, 0x42, 0x42, 0x40,
        0x20, 0x10, 0x08, 0x04, 0x42, 0x7E, 0x00, 0x00
    },
    {  // 字符 '3'
        0x00, 0x00, 0x00, 0x3C, 0x42, 0x42, 0x40, 0x20,
        0x18, 0x20, 0x40, 0x42, 0x42, 0x3C, 0x00, 0x00
    },
    {  // 字符 '4'
        0x00, 0x00, 0x00, 0x20, 0x30, 0x30, 0x28, 0x24,
        0x24, 0x22, 0xFE, 0x20, 0x20, 0xF8, 0x00, 0x00
    },
    {  // 字符 '5'
        0x00, 0x00, 0x00, 0x7E, 0x02, 0x02, 0x02, 0x1E,
        0x22, 0x40, 0x40, 0x42, 0x22, 0x1C, 0x00, 0x00
    },
    {  // 字符 '6'
        0x00, 0x00, 0x00, 0x18, 0x24, 0x02, 0x02, 0x3A,
        0x46, 0x42, 0x42, 0x42, 0x44, 0x38, 0x00, 0x00
    },
    {  // 字符 '7'
        0x00, 0x00, 0x00, 0x7E, 0x42, 0x20, 0x20, 0x10,
        0x10, 0x08, 0x08, 0x08, 0x08, 0x08, 0x00, 0x00
    },
    {  // 字符 '8'
        0x00, 0x00, 0x00, 0x3C, 0x42, 0x42, 0x42, 0x24,
        0x18, 0x24, 0x42, 0x42, 0x42, 0x3C, 0x00, 0x00
    },
    {  // 字符 '9'
        0x00, 0x00, 0x00, 0x1C, 0x22, 0x42, 0x42, 0x42,
        0x62, 0x5C, 0x40, 0x40, 0x24, 0x18, 0x00, 0x00
    }
};

// 中文星期汉字数据 (16x16点阵)
const unsigned char chinese_weekday_font_data[9][32] = {
    { // "星" (Index 0)
        0x00, 0x00, 0xF8, 0x0F, 0x08, 0x08, 0xF8, 0x0F,
        0x08, 0x08, 0xF8, 0x0F, 0x80, 0x00, 0x88, 0x00,
        0xF8, 0x1F, 0x84, 0x00, 0x82, 0x00, 0xF8, 0x0F,
        0x80, 0x00, 0x80, 0x00, 0xFE, 0x3F, 0x00, 0x00
    },
    { // "期" (Index 1)
        0x44, 0x00, 0x44, 0x3E, 0xFE, 0x22, 0x44, 0x22,
        0x44, 0x22, 0x7C, 0x3E, 0x44, 0x22, 0x44, 0x22,
        0x7C, 0x22, 0x44, 0x3E, 0x44, 0x22, 0xFF, 0x22,
        0x20, 0x21, 0x44, 0x21, 0x82, 0x28, 0x41, 0x10
    },
    { // "一" (Index 2)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF, 0x7F,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    },
    { // "二" (Index 3)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, 0x1F,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    },
    { // "三" (Index 4)
        0x00, 0x00, 0x00, 0x00, 0xFE, 0x3F, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFC, 0x1F,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00
    },
    { // "四" (Index 5)
        0x00, 0x00, 0x00, 0x00, 0xFE, 0x3F, 0x22, 0x22,
        0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22, 0x22,
        0x12, 0x22, 0x12, 0x3C, 0x0A, 0x20, 0x06, 0x20,
        0x02, 0x20, 0xFE, 0x3F, 0x02, 0x20, 0x00, 0x00
    },
    { // "五" (Index 6)
        0x00, 0x00, 0xFE, 0x3F, 0x40, 0x00, 0x40, 0x00,
        0x40, 0x00, 0x40, 0x00, 0xFC, 0x0F, 0x20, 0x08,
        0x20, 0x08, 0x20, 0x08, 0x20, 0x08, 0x10, 0x08,
        0x10, 0x08, 0x10, 0x08, 0xFF, 0x7F, 0x00, 0x00
    },
    { // "六" (Index 7)
        0x40, 0x00, 0x80, 0x00, 0x00, 0x01, 0x00, 0x01,
        0x00, 0x00, 0xFF, 0x7F, 0x00, 0x00, 0x00, 0x00,
        0x20, 0x02, 0x20, 0x04, 0x10, 0x08, 0x10, 0x10,
        0x08, 0x10, 0x04, 0x20, 0x02, 0x20, 0x00, 0x00
    },
    { // "日" (Index 8)
        0x00, 0x00, 0xF8, 0x0F, 0x08, 0x08, 0x08, 0x08,
        0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0xF8, 0x0F,
        0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08,
        0x08, 0x08, 0x08, 0x08, 0xF8, 0x0F, 0x08, 0x08
    }
};

// 英文星期字体数据 (8x16点阵，每个星期显示前3个字母)
const unsigned char english_weekday_font_data[7][48] = {
    { // SUNDAY (SUN)
        // S
        0x00,0x00,0x00,0x3C,0x66,0x62,0x06,0x0C,
        0x38,0x60,0x42,0x42,0x66,0x3C,0x00,0x00,
        // U
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63,
        0x42, 0x42, 0x42, 0x42, 0x62, 0xDC, 0x00, 0x00,
        // N
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3B,
        0x46, 0x42, 0x42, 0x42, 0x42, 0xE7, 0x00, 0x00,
    },
    { // MONDAY (MON)
        // M
        0x00,0x00,0x00,0x66,0x66,0x66,0x66,0x76,
        0x5E,0x5A,0x5A,0x5A,0x5A,0x4A,0x00,0x00,
        // O
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C,
        0x42, 0x42, 0x42, 0x42, 0x42, 0x3C, 0x00, 0x00,
        // N
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3B,
        0x46, 0x42, 0x42, 0x42, 0x42, 0xE7, 0x00, 0x00,
    },
    { // TUESDAY (TUE)
        // T
        0x00,0x00,0x00,0x7E,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x00,0x00,
        // U
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63,
        0x42, 0x42, 0x42, 0x42, 0x62, 0xDC, 0x00, 0x00,
        // E
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C,
        0x42, 0x42, 0x7E, 0x02, 0x42, 0x3C, 0x00, 0x00,    
    },
    { // WEDNESDAY (WED)
        // W
        0x00,0x00,0x00,0xD9,0x5B,0x5B,0x5A,0x5A,
        0x56,0x56,0x66,0x66,0x26,0x26,0x00,0x00,
        // E
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x3C,
        0x42, 0x42, 0x7E, 0x02, 0x42, 0x3C, 0x00, 0x00,    
        // D
        0x00, 0x00, 0x00, 0x00, 0x60, 0x40, 0x40, 0x7C,
        0x42, 0x42, 0x42, 0x42, 0x62, 0xDC, 0x00, 0x00,
    },
    { // THURSDAY (THU)
        // T
        0x00,0x00,0x00,0x7E,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x00,0x00,
        // H
        0x00, 0x00, 0x00, 0x00, 0x03, 0x02, 0x02, 0x3A,
        0x46, 0x42, 0x42, 0x42, 0x42, 0xE7, 0x00, 0x00,
        // U
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x63,
        0x42, 0x42, 0x42, 0x42, 0x62, 0xDC, 0x00, 0x00,
    },
    { // FRIDAY (FRI)
        // F
        0x00,0x00,0x00,0x7E,0x02,0x02,0x02,0x02,
        0x3E,0x02,0x02,0x02,0x02,0x02,0x00,0x00,
        // R
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x77,
        0x4C, 0x04, 0x04, 0x04, 0x04, 0x1F, 0x00, 0x00,
        // I
        0x00, 0x00, 0x00, 0x0C, 0x0C, 0x00, 0x00, 0x0E,
        0x08, 0x08, 0x08, 0x08, 0x08, 0x3E, 0x00, 0x00
    },
    { // SATURDAY (SAT)
        // S
        0x00,0x00,0x00,0x3C,0x66,0x62,0x06,0x0C,
        0x38,0x60,0x42,0x42,0x66,0x3C,0x00,0x00,
        // A
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x1C,
        0x22, 0x30, 0x2C, 0x22, 0x32, 0x6C, 0x00, 0x00,
        // T
        0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x08, 0x3E,
        0x08, 0x08, 0x08, 0x08, 0x48, 0x30, 0x00, 0x00
    }
};

// 32x32中文星期字体数据（星、期、日、一、二、三、四、五、六）- 已注释，改用32x16
/*
const unsigned char chinese_font[9][128] = {
    // 星 [0]
    {
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x01, 0x80, 0xFF, 0xFF, 0x03,
        0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0xFF, 0xFF, 0x01,
        0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
        0x80, 0xFF, 0xFF, 0x01, 0x80, 0x81, 0x80, 0x01, 0x00, 0x81, 0x03, 0x00, 0x80, 0x83, 0x01, 0x00,
        0x80, 0x81, 0x01, 0x03, 0xC0, 0xFF, 0xFF, 0x07, 0xC0, 0x80, 0x01, 0x00, 0x60, 0x80, 0x01, 0x00,
        0x20, 0x80, 0x81, 0x00, 0x10, 0x80, 0xC1, 0x01, 0x88, 0xFF, 0xFF, 0x03, 0x04, 0x80, 0x01, 0x00,
        0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x01, 0x08, 0x00, 0x80, 0x01, 0x1C,
        0xFC, 0xFF, 0xFF, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    },
    // 期 [1]
    {
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x20, 0x00, 0x00, 0xC0, 0xE1, 0x08, 0x10,
        0xC0, 0x60, 0xF8, 0x3F, 0xC0, 0x60, 0x18, 0x18, 0xC0, 0x60, 0x19, 0x18, 0xFC, 0xFF, 0x1F, 0x18,
        0xC0, 0x60, 0x18, 0x18, 0xC0, 0x60, 0x18, 0x18, 0xC0, 0x60, 0x18, 0x18, 0xC0, 0x7F, 0xF8, 0x1F,
        0xC0, 0x60, 0x18, 0x18, 0xC0, 0x60, 0x18, 0x18, 0xC0, 0x60, 0x18, 0x18, 0xC0, 0x7F, 0x18, 0x18,
        0xC0, 0x60, 0x18, 0x18, 0xC0, 0x60, 0x18, 0x18, 0xC0, 0x60, 0xF8, 0x1F, 0xC0, 0x60, 0x19, 0x18,
        0xFC, 0xFF, 0x1B, 0x18, 0x80, 0x00, 0x18, 0x18, 0x80, 0x11, 0x08, 0x18, 0x80, 0x23, 0x0C, 0x18,
        0xC0, 0xE0, 0x0C, 0x18, 0x60, 0xC0, 0x06, 0x18, 0x60, 0xC0, 0x06, 0x18, 0x10, 0x00, 0x03, 0x18,
        0x08, 0x80, 0x01, 0x1F, 0x04, 0xC0, 0x00, 0x0C, 0x00, 0x20, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00
    },
    // 日 [2]
    {
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x00, 0x00, 0x01, 0x80, 0xFF, 0xFF, 0x03,
        0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
        0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
        0x80, 0x01, 0x80, 0x01, 0x80, 0xFF, 0xFF, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
        0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
        0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01,
        0x80, 0xFF, 0xFF, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x01, 0x80, 0x00, 0x80, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    },
    // 一 [3]
    {
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x0C, 0xFC, 0xFF, 0xFF, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    },
    // 二 [4]
    {
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x03, 0xE0, 0xFF, 0xFF, 0x07, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08,
        0x00, 0x00, 0x00, 0x1C, 0xFC, 0xFF, 0xFF, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    },
    // 三 [5]
    {
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x0E, 0xF0, 0xFF, 0xFF, 0x0F, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x80, 0x01, 0xC0, 0xFF, 0xFF, 0x03,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x18, 0xFE, 0xFF, 0xFF, 0x3F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    },
    // 四 [6]
    {
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x10, 0x00, 0x00, 0x04, 0xF0, 0xFF, 0xFF, 0x0F,
        0x30, 0x30, 0x0C, 0x0C, 0x30, 0x30, 0x0C, 0x0C, 0x30, 0x30, 0x0C, 0x0C, 0x30, 0x30, 0x0C, 0x0C,
        0x30, 0x30, 0x0C, 0x0C, 0x30, 0x30, 0x0C, 0x0C, 0x30, 0x30, 0x0C, 0x0C, 0x30, 0x10, 0x0C, 0x0C,
        0x30, 0x10, 0x0C, 0x0C, 0x30, 0x10, 0x0C, 0x0C, 0x30, 0x18, 0x0C, 0x0C, 0x30, 0x18, 0x0C, 0x0C,
        0x30, 0x18, 0x0C, 0x0C, 0x30, 0x18, 0x0C, 0x0C, 0x30, 0x08, 0x0C, 0x0C, 0x30, 0x0C, 0x0C, 0x0C,
        0x30, 0x04, 0xFC, 0x0D, 0x30, 0x06, 0xF8, 0x0F, 0x30, 0x03, 0x00, 0x0C, 0xB0, 0x01, 0x00, 0x0C,
        0x70, 0x00, 0x00, 0x0C, 0x30, 0x00, 0x00, 0x0C, 0x30, 0x00, 0x00, 0x0C, 0xF0, 0xFF, 0xFF, 0x0F,
        0x30, 0x00, 0x00, 0x0C, 0x30, 0x00, 0x00, 0x0C, 0x30, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00
    },
    // 五 [7]
    {
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x06,
        0xF0, 0xFF, 0xFF, 0x0F, 0x00, 0xC0, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00, 0x00, 0xC0, 0x00, 0x00,
        0x00, 0x40, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
        0x00, 0x60, 0x80, 0x00, 0xE0, 0xFF, 0xFF, 0x01, 0x00, 0x20, 0xC0, 0x00, 0x00, 0x30, 0xC0, 0x00,
        0x00, 0x30, 0xC0, 0x00, 0x00, 0x30, 0xC0, 0x00, 0x00, 0x30, 0xC0, 0x00, 0x00, 0x30, 0xC0, 0x00,
        0x00, 0x10, 0xC0, 0x00, 0x00, 0x18, 0xC0, 0x00, 0x00, 0x18, 0xC0, 0x00, 0x00, 0x18, 0xC0, 0x00,
        0x00, 0x18, 0xC0, 0x00, 0x00, 0x18, 0xC0, 0x00, 0x00, 0x18, 0xC0, 0x18, 0xFC, 0xFF, 0xFF, 0x3F,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    },
    // 六 [8]
    {
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x00, 0x00, 0x00, 0x60, 0x00, 0x00,
        0x00, 0xC0, 0x00, 0x00, 0x00, 0x80, 0x01, 0x00, 0x00, 0x80, 0x03, 0x00, 0x00, 0x80, 0x03, 0x00,
        0x00, 0x00, 0x03, 0x00, 0x00, 0x00, 0x01, 0x00, 0x00, 0x00, 0x00, 0x18, 0xFC, 0xFF, 0xFF, 0x3F,
        0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x00, 0x00, 0x18, 0x04, 0x00, 0x00, 0x38, 0x08, 0x00,
        0x00, 0x1C, 0x10, 0x00, 0x00, 0x1C, 0x20, 0x00, 0x00, 0x0C, 0x60, 0x00, 0x00, 0x06, 0xC0, 0x00,
        0x00, 0x06, 0x80, 0x01, 0x00, 0x03, 0x80, 0x03, 0x80, 0x01, 0x00, 0x07, 0x80, 0x01, 0x00, 0x0F,
        0xC0, 0x00, 0x00, 0x0E, 0x60, 0x00, 0x00, 0x0E, 0x30, 0x00, 0x00, 0x0C, 0x18, 0x00, 0x00, 0x0C,
        0x08, 0x00, 0x00, 0x00, 0x04, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
    }
};
*/

// 32x16中文星期字体数据（星、期、日、一、二、三、四、五、六）- 存储在Flash中
const unsigned char font_chinese[9][72] PROGMEM = {
  { // 星 (0)
    0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0x08, 0x10, 0xF8, 0x1F, 0x08, 0x10,
    0x08, 0x10, 0x08, 0x10, 0xF8, 0x1F, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10,
    0xF8, 0x1F, 0x08, 0x10, 0x88, 0x00, 0x10, 0x01, 0x90, 0x10, 0x88, 0x10,
    0xF8, 0x3F, 0x88, 0x00, 0x88, 0x00, 0x84, 0x00, 0x84, 0x08, 0xFA, 0x1F,
    0x82, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x20, 0x80, 0x60, 0x7E, 0x1F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 期 (1)
    0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0xC8, 0x00, 0x48, 0x7C, 0x48, 0x44,
    0x48, 0x45, 0xFE, 0x45, 0x48, 0x44, 0x48, 0x44, 0x48, 0x44, 0x78, 0x7C,
    0x48, 0x44, 0x48, 0x44, 0x48, 0x44, 0x48, 0x44, 0x78, 0x44, 0x48, 0x44,
    0x48, 0x44, 0x48, 0x7C, 0xFE, 0x43, 0x02, 0x42, 0x00, 0x42, 0x58, 0x42,
    0x88, 0x42, 0x88, 0x42, 0x84, 0x41, 0x04, 0x41, 0x02, 0x31, 0x82, 0x20,
    0x00, 0x20, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 日 (2)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0x00, 0xF8, 0x1F, 0x08, 0x10,
    0x08, 0x10, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10,
    0x08, 0x10, 0x08, 0x10, 0xF8, 0x1F, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10,
    0x08, 0x10, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10, 0x08, 0x10,
    0x08, 0x10, 0x08, 0x10, 0xF8, 0x1F, 0x08, 0x10, 0x08, 0x10, 0x08, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 一 (3)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x20, 0x00, 0x60, 0xFE, 0x7F, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 二 (4)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x10, 0xFC, 0x1F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x20, 0x00, 0x60, 0xFE, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 三 (5)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0xFC, 0x3F, 0x04, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08, 0xF8, 0x1F, 0x08, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x60, 0xFE, 0x7F, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 四 (6)
    0x00, 0x00, 0x04, 0x20, 0xFC, 0x3F, 0x44, 0x22, 0x44, 0x22, 0x44, 0x22,
    0x44, 0x22, 0x44, 0x22, 0x44, 0x22, 0x44, 0x22, 0x44, 0x22, 0x44, 0x22,
    0x44, 0x22, 0x44, 0x22, 0x44, 0x22, 0x44, 0x22, 0x24, 0x22, 0x24, 0x22,
    0x24, 0x22, 0x24, 0x22, 0x24, 0x3E, 0x14, 0x3C, 0x14, 0x20, 0x0C, 0x20,
    0x04, 0x20, 0x04, 0x20, 0x04, 0x20, 0xFC, 0x3F, 0x04, 0x20, 0x04, 0x20,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 五 (7)
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x30, 0xFC, 0x3F, 0x80, 0x00,
    0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x00, 0x40, 0x00,
    0x40, 0x08, 0x40, 0x08, 0xF8, 0x0F, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08,
    0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x40, 0x08, 0x20, 0x08,
    0x20, 0x08, 0x20, 0x68, 0xFE, 0x7F, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // 六 (8)
    0x00, 0x00, 0x00, 0x00, 0x40, 0x00, 0x80, 0x00, 0x80, 0x00, 0x80, 0x01,
    0x80, 0x01, 0x00, 0x01, 0x00, 0x01, 0x00, 0x00, 0x00, 0x60, 0xFE, 0x7F,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x40, 0x02, 0x60, 0x02, 0x20, 0x04,
    0x20, 0x04, 0x20, 0x08, 0x30, 0x08, 0x10, 0x18, 0x10, 0x10, 0x08, 0x30,
    0x08, 0x30, 0x08, 0x20, 0x04, 0x20, 0x04, 0x20, 0x02, 0x20, 0x02, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00
  }
};

// 32x8数字字体数据（冒号、0-9）- 存储在Flash中
const unsigned char number_font[11][32] PROGMEM = {
  { // [0] = ':'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,
    0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x18, 0x18,
    0x18, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18,
    0x18, 0x18, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [1] = '0'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x24,
    0x24, 0x24, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42,
    0x42, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42, 0x24,
    0x24, 0x24, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [2] = '1'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x08,
    0x0E, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08,
    0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08,
    0x08, 0x08, 0x3E, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [3] = '2'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x38, 0x26,
    0x42, 0x42, 0x42, 0x42, 0x42, 0x40, 0x40, 0x20,
    0x20, 0x10, 0x10, 0x08, 0x08, 0x04, 0x44, 0x44,
    0x42, 0x7E, 0x7E, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [4] = '3'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x26,
    0x42, 0x42, 0x42, 0x42, 0x40, 0x20, 0x20, 0x18,
    0x20, 0x20, 0x40, 0x40, 0x40, 0x42, 0x42, 0x42,
    0x22, 0x22, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [5] = '4'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x20, 0x20,
    0x30, 0x30, 0x28, 0x28, 0x28, 0x24, 0x24, 0x24,
    0x22, 0x22, 0x22, 0x22, 0x7E, 0x20, 0x20, 0x20,
    0x20, 0x20, 0x30, 0x78, 0x00, 0x00, 0x00, 0x00
  },
  { // [6] = '5'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7E, 0x7E,
    0x02, 0x02, 0x02, 0x02, 0x02, 0x1A, 0x26, 0x22,
    0x42, 0x40, 0x40, 0x40, 0x40, 0x42, 0x42, 0x22,
    0x22, 0x22, 0x1C, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [7] = '6'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x24,
    0x24, 0x22, 0x02, 0x02, 0x02, 0x02, 0x1A, 0x26,
    0x26, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42,
    0x24, 0x14, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [8] = '7'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x7E, 0x7E,
    0x22, 0x22, 0x22, 0x20, 0x10, 0x10, 0x10, 0x10,
    0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08, 0x08,
    0x08, 0x08, 0x08, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [9] = '8'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x24,
    0x42, 0x42, 0x42, 0x42, 0x42, 0x46, 0x24, 0x1C,
    0x18, 0x34, 0x24, 0x42, 0x42, 0x42, 0x42, 0x42,
    0x42, 0x24, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00
  },
  { // [10] = '9'
    0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x18, 0x14,
    0x24, 0x22, 0x42, 0x42, 0x42, 0x42, 0x42, 0x42,
    0x62, 0x54, 0x58, 0x40, 0x40, 0x40, 0x20, 0x24,
    0x24, 0x24, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00
  }
};

// 32高×16宽英文星期字体数据（21个字符：每个星期前三个字母的点阵数据）- 存储在Flash中
const unsigned char en_font[21][64] PROGMEM = {
  { // 0: S
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF0,0x13,0x18,0x1E,
        0x0C,0x18,0x06,0x18,0x06,0x10,0x06,0x10,0x06,0x00,0x0E,0x00,0x3C,0x00,0xF8,0x00,
        0xE0,0x03,0x80,0x0F,0x00,0x1E,0x00,0x18,0x00,0x38,0x02,0x30,0x02,0x30,0x06,0x30,
        0x04,0x30,0x0C,0x18,0x1C,0x0C,0xE4,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 1: u
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x1E,0x1E,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x1C,0x30,0x7A,0xE0,0x09,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 2: n
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x90,0x07,0x5E,0x0C,0x38,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x7E,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 3: M
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0F,0xF0,0x1C,0x38,
        0x1C,0x38,0x1C,0x38,0x1C,0x38,0x1C,0x34,0x34,0x34,0x34,0x34,0x34,0x34,0x34,0x32,
        0x34,0x32,0x64,0x32,0x64,0x32,0x64,0x32,0x64,0x31,0x44,0x31,0xC4,0x31,0xC4,0x31,
        0xC4,0x30,0xC4,0x30,0x84,0x30,0x8F,0xFC,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 4: o
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0x03,0x30,0x0C,0x10,0x18,
        0x18,0x18,0x08,0x30,0x0C,0x30,0x0C,0x30,0x0C,0x30,0x0C,0x30,0x0C,0x30,0x18,0x18,
        0x18,0x18,0x30,0x0C,0xC0,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 5: n (同2)
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x90,0x07,0x5E,0x0C,0x38,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x7E,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 6: T
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFC,0x3F,0x8C,0x21,
        0x84,0x61,0x82,0x41,0x82,0x41,0x80,0x01,0x80,0x00,0x80,0x01,0x80,0x01,0x80,0x01,
        0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,
        0x80,0x01,0x80,0x01,0x80,0x01,0xE0,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 7: u (第二个u)
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x1E,0x1E,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x1C,0x30,0x7A,0xE0,0x09,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 8: e
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0x03,0x30,0x0C,0x10,0x18,
        0x18,0x10,0x0C,0x30,0x0C,0x30,0x0C,0x30,0xFC,0x3F,0x0C,0x00,0x0C,0x00,0x0C,0x00,
        0x18,0x20,0x18,0x10,0x70,0x18,0xC0,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 9: W
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xCF,0xF3,0x86,0x61,
        0x86,0x21,0x84,0x21,0x04,0x21,0x0C,0x23,0x8C,0x23,0x8C,0x23,0x8C,0x13,0x8C,0x13,
        0x88,0x13,0x48,0x12,0x58,0x16,0x58,0x16,0x58,0x0E,0x38,0x0E,0x30,0x0E,0x30,0x0E,
        0x30,0x0C,0x30,0x04,0x10,0x04,0x10,0x04,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 10: e (第二个e)
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0x03,0x30,0x0C,0x10,0x18,
        0x18,0x10,0x0C,0x30,0x0C,0x30,0x0C,0x30,0xFC,0x3F,0x0C,0x00,0x0C,0x00,0x0C,0x00,
        0x18,0x20,0x18,0x10,0x70,0x18,0xC0,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 11: d
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x00,0x1E,0x00,0x18,
        0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0xE0,0x1B,0x30,0x1C,0x18,0x18,
        0x18,0x18,0x0C,0x18,0x0C,0x18,0x0C,0x18,0x0C,0x18,0x0C,0x18,0x0C,0x18,0x08,0x18,
        0x18,0x1C,0x30,0x7A,0xE0,0x09,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 12: T
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFC,0x3F,0x8C,0x21,
        0x84,0x61,0x82,0x41,0x82,0x41,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,
        0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,
        0x80,0x01,0x80,0x01,0x80,0x01,0xE0,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 13: h
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x00,0x1E,0x00,0x18,0x00,
        0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x18,0x00,0x98,0x07,0x58,0x0C,0x38,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x7E,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 14: u (第三个u)
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x10,0x10,0x1E,0x1E,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,0x18,
        0x18,0x18,0x18,0x1C,0x30,0x7A,0xE0,0x09,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 15: F
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xFE,0x3F,0x18,0x38,
        0x18,0x20,0x18,0x40,0x18,0x40,0x18,0x00,0x18,0x00,0x18,0x08,0x18,0x08,0x18,0x0C,
        0xF8,0x0F,0x18,0x0C,0x18,0x08,0x18,0x08,0x18,0x08,0x18,0x00,0x18,0x00,0x18,0x00,
        0x18,0x00,0x18,0x00,0x18,0x00,0x7E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 16: r
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x60,0x38,0x7E,0x66,0x60,0x61,
        0xE0,0x01,0xE0,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,0x60,0x00,
        0x60,0x00,0x60,0x00,0x60,0x00,0xFE,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 17: i
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x80,0x01,0xC0,0x03,
        0x80,0x01,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x01,0xF8,0x01,0x80,0x01,0x80,0x01,
        0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,0x80,0x01,
        0x80,0x01,0x80,0x01,0x80,0x01,0xF8,0x1F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 18: S (第二个S)
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF0,0x13,0x18,0x1E,
        0x0C,0x18,0x06,0x18,0x06,0x10,0x06,0x10,0x06,0x00,0x0E,0x00,0x3C,0x00,0xF8,0x00,
        0xE0,0x03,0x80,0x0F,0x00,0x1E,0x00,0x18,0x00,0x38,0x02,0x30,0x02,0x30,0x06,0x30,
        0x04,0x30,0x0C,0x18,0x1C,0x0C,0xE4,0x07,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 19: a
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0x07,0x18,0x0C,0x0C,0x18,
        0x0C,0x18,0x0C,0x18,0x00,0x1C,0xE0,0x1B,0x38,0x18,0x0C,0x18,0x06,0x18,0x06,0x18,
        0x06,0x18,0x06,0x98,0x0C,0x9E,0xF8,0x71,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    },
    { // 20: t
        0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
        0x80,0x00,0x80,0x00,0x80,0x00,0xC0,0x00,0xE0,0x00,0xFC,0x1F,0xC0,0x00,0xC0,0x00,
        0xC0,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x00,0xC0,0x00,
        0xC0,0x20,0xC0,0x20,0x80,0x11,0x00,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00
    }
};

// 显示区域相关变量定义
DisplayRegion display_regions[MAX_REGIONS];
TimeDisplayInfo time_info;

// 初始化LED矩阵屏
bool initLEDMatrix()
{
    HUB75_I2S_CFG mxconfig(
        SCREEN_WIDTH,       // 面板宽度
        SCREEN_HEIGHT,      // 面板高度
        PANEL_CHAIN_LENGTH  // 链式面板数量
    );

    // 配置引脚映射
    mxconfig.gpio.r1 = PIN_R1;
    mxconfig.gpio.g1 = PIN_G1;
    mxconfig.gpio.b1 = PIN_B1;
    mxconfig.gpio.r2 = PIN_R2;
    mxconfig.gpio.g2 = PIN_G2;
    mxconfig.gpio.b2 = PIN_B2;

    mxconfig.gpio.a = PIN_A;
    mxconfig.gpio.b = PIN_B;
    mxconfig.gpio.c = PIN_C;
    mxconfig.gpio.d = PIN_D;
    mxconfig.gpio.e = PIN_E;

    mxconfig.gpio.lat = PIN_LAT;
    mxconfig.gpio.oe = PIN_OE;
    mxconfig.gpio.clk = PIN_CLK;

    // 配置选项
    mxconfig.clkphase = false;
    mxconfig.driver = HUB75_I2S_CFG::MATRIX_DRIVER_CHIP;

    // 创建显示对象
    dma_display = new MatrixPanel_I2S_DMA(mxconfig);

    if (!dma_display->begin()) {
        Serial.println("LED matrix initialization failed");
        return false;
    }

    // 设置亮度
    dma_display->setBrightness8(MATRIX_BRIGHTNESS);
    dma_display->clearScreen();
    dma_display->fillScreen(dma_display->color565(0, 0, 0));

    Serial.println("LED matrix initialized successfully");
    Serial.printf("Screen size: %dx%d, Brightness: %d\n", SCREEN_WIDTH, SCREEN_HEIGHT, MATRIX_BRIGHTNESS);
    return true;
}

// 播放GIF动画
bool playGIF(const char* filename)
{
    // 停止当前播放的GIF
    stopGIF();

    // 打开GIF文件
    gifFile = LittleFS.open(filename, "r");
    if (!gifFile) {
       printf("Cannot open GIF file: %s\n", filename);
        return false;
    }

    // 读取文件到内存
    size_t fileSize = gifFile.size();
    uint8_t* gifData = (uint8_t*)malloc(fileSize);
    if (!gifData) {
        printf("Memory allocation failed");
        gifFile.close();
        return false;
    }

    gifFile.read(gifData, fileSize);
    gifFile.close();

    // 初始化GIF解码器
    if (gif.open(gifData, fileSize, GIFDraw)) {
        printf("Starting GIF playback: %s\n", filename);
        printf("GIF size: %dx%d\n", gif.getCanvasWidth(), gif.getCanvasHeight());

        // 成功后更新全局内存指针
        currentGifData = gifData;
        currentGifDataSize = fileSize;

        gifPlaying = true;
        lastGifFrame = millis();
        return true;
    } else {
        printf("GIF decoder initialization failed: %s\n", filename);
        free(gifData);  // 失败时释放内存
        gifFile.close();
        return false;
    }
}

// 停止GIF播放
void stopGIF()
{
    if (gifPlaying) {
        gif.close();
        gifFile.close();
        gifPlaying = false;
        printf("GIF playback stopped");
    }

    // 释放之前malloc的内存
    if (currentGifData) {
        free(currentGifData);
        currentGifData = nullptr;
        currentGifDataSize = 0;
        printf("GIF memory freed");
    }
}

// GIF绘制回调函数
void GIFDraw(GIFDRAW *pDraw)
{
    uint8_t *s;
    uint16_t *d, *usPalette, usTemp[320];
    int x, y, iWidth;
    int actual_x, actual_y;  // 应用偏移后的实际坐标

    iWidth = pDraw->iWidth;
    if (iWidth > SCREEN_WIDTH)
        iWidth = SCREEN_WIDTH;

    usPalette = pDraw->pPalette;
    y = pDraw->iY + pDraw->y; // 当前行

    // 应用Y偏移量
    actual_y = y + gif_offset_y;

    // Y坐标边界检查
    if (actual_y >= SCREEN_HEIGHT || actual_y < 0 || pDraw->y >= pDraw->iHeight)
        return;

    s = pDraw->pPixels;

    if (pDraw->ucDisposalMethod == 2) {
        // 恢复到背景色
        for (x = 0; x < iWidth; x++) {
            if (s[x] == pDraw->ucTransparent)
                s[x] = pDraw->ucBackground;
        }
        pDraw->ucHasTransparency = 0;
    }

    // 应用调色板并绘制到屏幕
    if (pDraw->ucHasTransparency) {
        uint8_t ucTransparent = pDraw->ucTransparent;
        uint8_t c;
        int x, iCount;
        for (x = 0; x < iWidth; x++) {
            c = s[x];
            if (c != ucTransparent) {
                // 应用X偏移量并进行边界检查
                actual_x = pDraw->iX + x + gif_offset_x;
                if (actual_x >= 0 && actual_x < SCREEN_WIDTH) {
                    uint16_t color = usPalette[c];
                    EnhancedTextPixelMask::smartDrawGifPixel(actual_x, actual_y, color);
                }
            }
        }
    } else {
        s = pDraw->pPixels;
        for (x = 0; x < iWidth; x++) {
            // 应用X偏移量并进行边界检查
            actual_x = pDraw->iX + x + gif_offset_x;
            if (actual_x >= 0 && actual_x < SCREEN_WIDTH) {
                uint16_t color = usPalette[s[x]];
                EnhancedTextPixelMask::smartDrawGifPixel(actual_x, actual_y, color);
            }
        }
    }
}

// 更新GIF动画
void updateGIF()
{
    if (!gifPlaying) {
        return;
    }

    unsigned long currentTime = millis();
    if (currentTime - lastGifFrame >= gifFrameDelay) {
        if (gif.playFrame(true, NULL) == 0) {
            // GIF播放完成，重新开始
            gif.reset();
        }
        lastGifFrame = currentTime;
    }
}

// 设置GIF显示偏移量
void setGifDisplayOffset(int offset_x, int offset_y)
{
    // 边界检查，确保偏移量在合理范围内
    gif_offset_x = constrain(offset_x, -SCREEN_WIDTH, SCREEN_WIDTH);
    gif_offset_y = constrain(offset_y, -SCREEN_HEIGHT, SCREEN_HEIGHT);

    Serial.printf("GIF display offset set to: (%d, %d)\n", gif_offset_x, gif_offset_y);
}

// 重置GIF显示偏移量为默认值
void resetGifDisplayOffset()
{
    gif_offset_x = GIF_DEFAULT_OFFSET_X;
    gif_offset_y = GIF_DEFAULT_OFFSET_Y;
    Serial.println("GIF display offset reset to default values");
}

// 获取当前偏移量
void getGifDisplayOffset(int* offset_x, int* offset_y)
{
    if (offset_x) *offset_x = gif_offset_x;
    if (offset_y) *offset_y = gif_offset_y;
}

// 初始化显示区域系统
void initDisplayRegions() {
    // 初始化GIF区域 (左侧 16x32)
    initDisplayRegion(&display_regions[REGION_GIF], 0, 0, 15, 31, COLOR_WHITE, COLOR_BLACK);

    // 初始化时间区域 (右上 48x16)
    initDisplayRegion(&display_regions[REGION_TIME], 16, 0, 63, 15, COLOR_RED, COLOR_BLACK);

    // 初始化星期区域 (右下 48x16)
    initDisplayRegion(&display_regions[REGION_WEEKDAY], 16, 16, 63, 31, COLOR_BLUE, COLOR_BLACK);

    // 初始化大屏区域 (右侧 48x32) - 大屏模式使用
    initDisplayRegion(&display_regions[REGION_BIG_SCREEN], 16, 0, 63, 31, big_screen_time_color, big_screen_bg_color);

    // 根据当前模式设置区域可见性
    updateRegionVisibility();

    printf("Display regions initialized: Small=%d, Big=%d\n", DISPLAY_MODE_SMALL, DISPLAY_MODE_BIG);
}

// 设置时间显示区域
void setupTimeRegions() {
    // 初始化时间信息
    time_info.hour = DEFAULT_HOUR;
    time_info.minute = DEFAULT_MINUTE;
    time_info.weekday = 1;  // 默认周一
    time_info.time_color = COLOR_RED;
    time_info.weekday_color = COLOR_BLUE;

    // 清除时间和星期区域
    clearRegion(&display_regions[REGION_TIME]);
    clearRegion(&display_regions[REGION_WEEKDAY]);

    // 绘制初始时间
    drawTimeToRegion(&display_regions[REGION_TIME], time_info.hour, time_info.minute);

    // 绘制初始星期 (使用当前全局语言设置)
    drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], time_info.weekday, current_display_language);

    Serial.printf("Time regions setup: %02d:%02d\n", time_info.hour, time_info.minute);
}

// 初始化单个显示区域
void initDisplayRegion(DisplayRegion* region, uint8_t x1, uint8_t y1,
                      uint8_t x2, uint8_t y2, uint16_t text_color, uint16_t bg_color) {
    region->x1 = x1;
    region->y1 = y1;
    region->x2 = x2;
    region->y2 = y2;
    region->cursor_x = 0;
    region->cursor_y = 0;
    region->text_color = text_color;
    region->bg_color = bg_color;
    region->auto_clear = true;
    region->visible = true;
    region->update_flag = 1;
    region->bitmap_data = nullptr;
    region->data_width = 0;
    region->data_height = 0;
}

// 清除区域
void clearRegion(DisplayRegion* region) {
    if (!region->visible || !dma_display) return;

    for (uint8_t y = region->y1; y <= region->y2; y++) {
        for (uint8_t x = region->x1; x <= region->x2; x++) {
            dma_display->drawPixel(x, y, region->bg_color);
        }
    }
}

// 检查点是否在区域内
bool isPointInRegion(DisplayRegion* region, uint8_t x, uint8_t y) {
    return (x >= region->x1 && x <= region->x2 && y >= region->y1 && y <= region->y2);
}

// 在区域内绘制字符
void drawCharToRegion(DisplayRegion* region, uint8_t char_index, int8_t x, int8_t y) {
    if (!region->visible || !dma_display || char_index >= 11) return;

    const uint8_t* char_data = font_data[char_index];

    for (int row = 0; row < FONT_HEIGHT; row++) {
        uint8_t line_data = char_data[row];
        for (int col = 0; col < FONT_WIDTH; col++) {
            int pixel_x = region->x1 + x + col;
            int pixel_y = region->y1 + y + row;

            // 边界检查
            if (pixel_x > region->x2 || pixel_y > region->y2 ||
                pixel_x < region->x1 || pixel_y < region->y1) continue;

            // 检查像素是否需要点亮 (修复镜像问题，从右到左读取位)
            if (line_data & (0x01 << col)) {
                EnhancedTextPixelMask::smartDrawTextPixel(pixel_x, pixel_y, region->text_color);
            }
            // 删除auto_clear分支，不再绘制背景像素
        }
    }
}

// 在区域内绘制星期汉字 (16x16字体)
void drawWeekdayCharToRegion(DisplayRegion* region, uint8_t char_index, int8_t x, int8_t y) {
    if (!region->visible || !dma_display || char_index >= 9) return;

    const uint8_t* char_data = chinese_weekday_font_data[char_index];

    for (int row = 0; row < WEEKDAY_FONT_HEIGHT; row++) {
        // 每行2个字节 (16位)
        uint8_t byte1 = char_data[row * 2];     // 低字节
        uint8_t byte2 = char_data[row * 2 + 1]; // 高字节

        // 先绘制低字节的8位
        for (int col = 0; col < 8; col++) {
            int pixel_x = region->x1 + x + col;
            int pixel_y = region->y1 + y + row;

            // 边界检查
            if (pixel_x > region->x2 || pixel_y > region->y2 ||
                pixel_x < region->x1 || pixel_y < region->y1) continue;

            // 检查像素是否需要点亮
            if (byte1 & (0x01 << col)) {
                EnhancedTextPixelMask::smartDrawTextPixel(pixel_x, pixel_y, region->text_color);
            }
            // 删除auto_clear分支，不再绘制背景像素
        }

        // 再绘制高字节的8位
        for (int col = 0; col < 8; col++) {
            int pixel_x = region->x1 + x + col + 8;
            int pixel_y = region->y1 + y + row;

            // 边界检查
            if (pixel_x > region->x2 || pixel_y > region->y2 ||
                pixel_x < region->x1 || pixel_y < region->y1) continue;

            // 检查像素是否需要点亮
            if (byte2 & (0x01 << col)) {
                EnhancedTextPixelMask::smartDrawTextPixel(pixel_x, pixel_y, region->text_color);
            }
            // 删除auto_clear分支，不再绘制背景像素
        }
    }
}

// 在区域内绘制英文星期字符 (8x16字体)
void drawEnglishWeekdayCharToRegion(DisplayRegion* region, uint8_t char_index, int8_t x, int8_t y) {
    if (!region->visible || !dma_display || char_index >= 7) return;

    const uint8_t* char_data = english_weekday_font_data[char_index];

    // 绘制3个字符 (每个星期的前3个字母)
    for (int char_offset = 0; char_offset < 3; char_offset++) {
        const uint8_t* single_char_data = char_data + (char_offset * 16);

        for (int row = 0; row < FONT_HEIGHT; row++) {
            uint8_t line_data = single_char_data[row];
            for (int col = 0; col < FONT_WIDTH; col++) {
                int pixel_x = region->x1 + x + (char_offset * FONT_WIDTH) + col;
                int pixel_y = region->y1 + y + row;

                // 边界检查
                if (pixel_x > region->x2 || pixel_y > region->y2 ||
                    pixel_x < region->x1 || pixel_y < region->y1) continue;

                // 检查像素是否需要点亮
                if (line_data & (0x01 << col)) {
                    EnhancedTextPixelMask::smartDrawTextPixel(pixel_x, pixel_y, region->text_color);
                }
                // 删除auto_clear分支，不再绘制背景像素
            }
        }
    }
}

// 在区域内绘制时间
void drawTimeToRegion(DisplayRegion* region, uint8_t hour, uint8_t minute) {
    if (!region->visible) return;

    // 智能清除区域（恢复GIF背景）
    EnhancedTextPixelMask::smartClearTextRegion(region->x1, region->y1,
                                               region->x2 - region->x1 + 1,
                                               region->y2 - region->y1 + 1);

    // 计算显示位置 (居中显示)
    int8_t start_x = 4;  // 左边留4像素边距
    int8_t start_y = 0;  // 顶部对齐

    // 绘制小时 (两位数)
    uint8_t hour_tens = hour / 10;
    uint8_t hour_ones = hour % 10;

    if (hour_tens > 0) {
        drawCharToRegion(region, hour_tens + 1, start_x, start_y);  // +1因为索引1是'0'
    }
    drawCharToRegion(region, hour_ones + 1, start_x + FONT_WIDTH, start_y);

    // 绘制冒号
    drawCharToRegion(region, 0, start_x + FONT_WIDTH * 2, start_y);  // 索引0是':'

    // 绘制分钟 (两位数)
    uint8_t minute_tens = minute / 10;
    uint8_t minute_ones = minute % 10;

    drawCharToRegion(region, minute_tens + 1, start_x + FONT_WIDTH * 3, start_y);
    drawCharToRegion(region, minute_ones + 1, start_x + FONT_WIDTH * 4, start_y);
}

// 在区域内绘制星期
void drawWeekdayToRegion(DisplayRegion* region, uint8_t weekday) {
    if (!region->visible || weekday > 6) return;

    // 智能清除区域（恢复GIF背景）
    EnhancedTextPixelMask::smartClearTextRegion(region->x1, region->y1,
                                               region->x2 - region->x1 + 1,
                                               region->y2 - region->y1 + 1);

    // 绘制"星期"两个字 (固定位置)
    drawWeekdayCharToRegion(region, 0, 0, 0);   // "星" 字
    drawWeekdayCharToRegion(region, 1, 16, 0);  // "期" 字

    // 根据星期数绘制对应的数字汉字
    uint8_t weekday_char_index;
    switch (weekday) {
        case 0: weekday_char_index = 8; break;  // 周日 -> "日"
        case 1: weekday_char_index = 2; break;  // 周一 -> "一"
        case 2: weekday_char_index = 3; break;  // 周二 -> "二"
        case 3: weekday_char_index = 4; break;  // 周三 -> "三"
        case 4: weekday_char_index = 5; break;  // 周四 -> "四"
        case 5: weekday_char_index = 6; break;  // 周五 -> "五"
        case 6: weekday_char_index = 7; break;  // 周六 -> "六"
        default: weekday_char_index = 2; break; // 默认 -> "一"
    }

    // 绘制星期数字 (第三个字的位置)
    drawWeekdayCharToRegion(region, weekday_char_index, 32, 0);
}

// 在区域内绘制星期 (支持多语言)
void drawWeekdayToRegionWithLang(DisplayRegion* region, uint8_t weekday, WeekdayLanguage language) {
    if (!region->visible || weekday > 6) return;

    // 智能清除区域（恢复GIF背景）
    EnhancedTextPixelMask::smartClearTextRegion(region->x1, region->y1,
                                               region->x2 - region->x1 + 1,
                                               region->y2 - region->y1 + 1);

    switch (language) {
        case LANG_CHINESE:
            // 使用原有的中文显示逻辑
            drawWeekdayToRegion(region, weekday);
            break;

        case LANG_ENGLISH:
            // 绘制英文星期缩写 (3个字母，居中显示)
            {
                int region_width = region->x2 - region->x1 + 1;
                int text_width = 3 * FONT_WIDTH;  // 3个字符的宽度
                int start_x = (region_width - text_width) / 2;
                if (start_x < 0) start_x = 0;

                drawEnglishWeekdayCharToRegion(region, weekday, start_x, 0);
            }
            break;

        default:
            // 默认使用中文
            drawWeekdayToRegion(region, weekday);
            break;
    }
}

// 更新时间逻辑
void updateTimeLogic() {
    static unsigned long last_minute_update = 0;
    static uint8_t last_displayed_minute = 0xFF;
    static uint8_t last_displayed_hour = 0xFF;
    static uint8_t last_displayed_weekday = 0xFF;

    unsigned long now = millis();

    // 每分钟更新一次显示
    if (now - last_minute_update >= 60000) {  // 60秒 = 60000毫秒
        time_info.minute++;
        if (time_info.minute >= 60) {
            time_info.minute = 0;
            time_info.hour++;
            if (time_info.hour >= 24) {
                time_info.hour = 0;
                // 新的一天开始，更新星期
                time_info.weekday++;
                if (time_info.weekday > 6) {
                    time_info.weekday = 0;  // 周日重新开始
                }
                Serial.printf("New day! Weekday changed to: %d\n", time_info.weekday);
            }
        }
        last_minute_update = now;
    }

    // 检查是否需要更新时间显示
    if (time_info.minute != last_displayed_minute || time_info.hour != last_displayed_hour) {
        drawTimeToRegion(&display_regions[REGION_TIME], time_info.hour, time_info.minute);
        last_displayed_minute = time_info.minute;
        last_displayed_hour = time_info.hour;

        Serial.printf("Time updated: %02d:%02d\n", time_info.hour, time_info.minute);
    }

    // 检查是否需要更新星期显示
    if (time_info.weekday != last_displayed_weekday) {
        // 使用当前全局语言设置进行显示
        drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], time_info.weekday, current_display_language);
        last_displayed_weekday = time_info.weekday;

        // 根据当前语言输出调试信息
        const char* weekday_name = getWeekdayNameWithLang(time_info.weekday, current_display_language);
        if (current_display_language == LANG_CHINESE) {
            Serial.printf("Weekday updated: 星期%s\n", weekday_name);
        } else {
            Serial.printf("Weekday updated: %s\n", weekday_name);
        }
    }
}

// 更新时间显示
void updateTimeDisplay() {
    updateTimeLogic();
}

// 更新所有区域
void updateAllRegions() {
    // 时间区域在updateTimeDisplay中已经处理
    // GIF区域由原有的updateGIF()函数处理
    // 星期区域暂时不需要频繁更新
}

// 设置时间
void setTime(uint8_t hour, uint8_t minute) {
    if (hour < 24 && minute < 60) {
        time_info.hour = hour;
        time_info.minute = minute;
        drawTimeToRegion(&display_regions[REGION_TIME], hour, minute);
        Serial.printf("Time set to: %02d:%02d\n", hour, minute);
    }
}

// 同时设置时间和星期 (使用当前全局语言设置，保持向后兼容)
void setTimeAndWeekday(uint8_t hour, uint8_t minute, uint8_t weekday) {
    if (hour < 24 && minute < 60 && weekday <= 6) {
        time_info.hour = hour;
        time_info.minute = minute;
        time_info.weekday = weekday;

        drawTimeToRegion(&display_regions[REGION_TIME], hour, minute);
        // 使用当前全局语言设置进行显示
        drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], weekday, current_display_language);

        // 根据当前语言输出调试信息
        const char* weekday_name = getWeekdayNameWithLang(weekday, current_display_language);
        if (current_display_language == LANG_CHINESE) {
            Serial.printf("Time and weekday set to: %02d:%02d 星期%s\n", hour, minute, weekday_name);
        } else {
            Serial.printf("Time and weekday set to: %02d:%02d %s\n", hour, minute, weekday_name);
        }
    }
}

// 同时设置时间和星期 (支持多语言)
void setTimeAndWeekdayWithLang(uint8_t hour, uint8_t minute, uint8_t weekday, WeekdayLanguage language) {
    if (hour < 24 && minute < 60 && weekday <= 6) {
        time_info.hour = hour;
        time_info.minute = minute;
        time_info.weekday = weekday;

        // 更新全局语言状态
        current_display_language = language;

        // 绘制时间 (时间显示不受语言影响)
        drawTimeToRegion(&display_regions[REGION_TIME], hour, minute);

        // 绘制星期 (使用指定语言)
        drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], weekday, language);

        // 输出调试信息 (根据语言选择格式)
        const char* weekday_name = getWeekdayNameWithLang(weekday, language);
        if (language == LANG_CHINESE) {
           printf("Time and weekday set to: %02d:%02d 星期%s\n", hour, minute, weekday_name);
        } else {
           printf("Time and weekday set to: %02d:%02d %s\n", hour, minute, weekday_name);
        }
    }
}

// 设置时间颜色
void setTimeColor(uint16_t color) {
    time_info.time_color = color;
    display_regions[REGION_TIME].text_color = color;
    drawTimeToRegion(&display_regions[REGION_TIME], time_info.hour, time_info.minute);
}

// 设置星期颜色
void setWeekdayColor(uint16_t color) {
    time_info.weekday_color = color;
    display_regions[REGION_WEEKDAY].text_color = color;
    // 使用当前全局语言设置重新绘制
    drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], time_info.weekday, current_display_language);
}

// 设置星期 (使用当前全局语言设置，保持向后兼容)
void setWeekday(uint8_t weekday) {
    if (weekday <= 6) {
        time_info.weekday = weekday;
        // 使用当前全局语言设置进行显示
        drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], weekday, current_display_language);

        const char* weekday_name = getWeekdayNameWithLang(weekday, current_display_language);
        if (current_display_language == LANG_CHINESE) {
            Serial.printf("Weekday updated: 星期%s\n", weekday_name);
        } else {
            Serial.printf("Weekday updated: %s\n", weekday_name);
        }
    }
}

// 设置星期 (支持多语言)
void setWeekdayWithLanguage(uint8_t weekday, WeekdayLanguage language) {
    if (weekday <= 6) {
        time_info.weekday = weekday;

        // 更新全局语言状态
        current_display_language = language;

        drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], weekday, language);

        const char* weekday_name = getWeekdayNameWithLang(weekday, language);
        if (language == LANG_CHINESE) {
            Serial.printf("Weekday set to: 星期%s\n", weekday_name);
        } else {
            Serial.printf("Weekday set to: %s\n", weekday_name);
        }
    }
}

// RGB888转RGB565
uint16_t rgb888to565(uint8_t r, uint8_t g, uint8_t b) {
    return ((r & 0xF8) << 8) | ((g & 0xFC) << 3) | (b >> 3);
}

// 获取字符索引
uint8_t getCharIndex(char c) {
    if (c == ':') return 0;
    if (c >= '0' && c <= '9') return c - '0' + 1;
    return 0;  // 默认返回冒号
}

// 获取星期名称 (中文)
const char* getWeekdayName(uint8_t weekday) {
    static const char* weekday_names[] = {"日", "一", "二", "三", "四", "五", "六"};
    if (weekday <= 6) {
        return weekday_names[weekday];
    }
    return "一";  // 默认返回周一
}

// 获取星期名称 (支持多语言)
const char* getWeekdayNameWithLang(uint8_t weekday, WeekdayLanguage language) {
    switch (language) {
        case LANG_CHINESE:
            {
                static const char* chinese_names[] = {"日", "一", "二", "三", "四", "五", "六"};
                if (weekday <= 6) {
                    return chinese_names[weekday];
                }
                return "一";
            }

        case LANG_ENGLISH:
            {
                static const char* english_names[] = {"SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"};
                if (weekday <= 6) {
                    return english_names[weekday];
                }
                return "MON";
            }

        default:
            return getWeekdayName(weekday);
    }
}

bool changeStartupGIF(const char* gifName)
{
    // 构建完整路径
    String fullPath = String(GIF_STORAGE_PATH) + gifName;

   printf("Attempting to change startup GIF to: %s\n", fullPath.c_str());

    // 检查文件是否存在
    if (!LittleFS.exists(fullPath)) {
        printf("Startup GIF not found: %s\n", fullPath.c_str());
        return false;
    }

    // 检查当前内存状态
    printf("Free heap before change: %d bytes\n", ESP.getFreeHeap());

    // 停止当前播放的GIF
    stopGIF();
    delay(100);  // 确保清理完成

    printf("Free heap after cleanup: %d bytes\n", ESP.getFreeHeap());

    // 播放新的开机动画
    if (playGIF(fullPath.c_str())) {
        printf("Startup animation successfully changed to: %s\n", gifName);
        return true;
    } else {
        printf("Failed to play startup GIF: %s\n", gifName);
        return false;
    }
}

// 检查GIF文件
bool checkGifFiles()
{
    printf("Checking GIF files...");

    // 检查开机动画
    if (!LittleFS.exists(GIF_STARTUP_FILE)) {
        printf("Missing startup GIF: %s\n", GIF_STARTUP_FILE);
        return false;
    }

    // 检查要切换的GIF
    String targetGif = String(GIF_STORAGE_PATH) + "R.gif";
    if (!LittleFS.exists(targetGif)) {
        printf("Missing target GIF: %s\n", targetGif.c_str());
        return false;
    }

    // 检查文件大小
    File file = LittleFS.open(targetGif, "r");
    if (file) {
        size_t fileSize = file.size();
        file.close();
        printf("R.gif size: %d bytes\n", fileSize);

        // 检查是否有足够内存
        size_t freeHeap = ESP.getFreeHeap();
        printf("Free heap: %d bytes\n", freeHeap);

        if (fileSize > freeHeap / 2) {  // 保留一半内存给其他用途
            printf("Warning: R.gif may be too large for available memory");
            return false;
        }
    }

    printf("All GIF files check passed");
    return true;
}

// 全局语言管理函数实现
void setSystemDisplayLanguage(WeekdayLanguage language) {
    if (language < LANG_COUNT) {
        current_display_language = language;
        // 重新绘制当前星期显示以应用新语言
        updateWeekdayWithCurrentLanguage(time_info.weekday);
        Serial.printf("System display language changed to: %d\n", language);
    }
}

WeekdayLanguage getSystemDisplayLanguage() {
    return current_display_language;
}

void updateWeekdayWithCurrentLanguage(uint8_t weekday) {
    if (weekday <= 6) {
        drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], weekday, current_display_language);
    }
}

// ==================== 大屏模式核心实现 ====================

// 更新区域可见性
void updateRegionVisibility() {
    if (current_display_mode == DISPLAY_MODE_SMALL) {
        // 小屏模式：显示原有三分区
        display_regions[REGION_TIME].visible = true;
        display_regions[REGION_WEEKDAY].visible = true;
        display_regions[REGION_BIG_SCREEN].visible = false;
        printf("Region visibility: Small mode activated\n");
    } else {
        // 大屏模式：只显示大屏区域
        display_regions[REGION_TIME].visible = false;
        display_regions[REGION_WEEKDAY].visible = false;
        display_regions[REGION_BIG_SCREEN].visible = true;
        printf("Region visibility: Big mode activated\n");
    }
}

// 强制清除整个区域
void forceClearRegion(DisplayRegion* region) {
    if (!region->visible || !dma_display) return;

    printf("Force clearing region (%d,%d) to (%d,%d)\n",
           region->x1, region->y1, region->x2, region->y2);

    // 逐像素清除整个区域
    for (int y = region->y1; y <= region->y2; y++) {
        for (int x = region->x1; x <= region->x2; x++) {
            dma_display->drawPixel(x, y, region->bg_color);
        }
    }
}

// 设置显示模式
void setDisplayMode(DisplayMode mode) {
    if (mode != current_display_mode) {
        printf("Switching display mode from %s to %s\n",
               (current_display_mode == DISPLAY_MODE_SMALL) ? "SMALL" : "BIG",
               (mode == DISPLAY_MODE_SMALL) ? "SMALL" : "BIG");

        // 先强制清除相关区域
        if (mode == DISPLAY_MODE_BIG) {
            // 切换到大屏模式：清除小屏区域
            forceClearRegion(&display_regions[REGION_TIME]);
            forceClearRegion(&display_regions[REGION_WEEKDAY]);
        } else {
            // 切换到小屏模式：清除大屏区域
            forceClearRegion(&display_regions[REGION_BIG_SCREEN]);
        }

        current_display_mode = mode;
        updateRegionVisibility();

        if (mode == DISPLAY_MODE_BIG) {
            // 重置大屏状态
            big_screen_state = BIG_SCREEN_TIME;
            big_screen_last_switch = millis();

            // 同步时间信息（确保大屏显示最新时间）
            printf("Big screen mode activated, current time: %02d:%02d, weekday: %d\n",
                   time_info.hour, time_info.minute, time_info.weekday);

            // 强制重绘（清除静态缓存）
            drawBigScreenTime(true);  // 模式切换时强制重绘
            printf("Display mode changed to BIG\n");
        } else {
            // 恢复小屏显示
            drawTimeToRegion(&display_regions[REGION_TIME], time_info.hour, time_info.minute);
            drawWeekdayToRegionWithLang(&display_regions[REGION_WEEKDAY], time_info.weekday, current_display_language);
            printf("Display mode changed to SMALL\n");
        }
    }
}

// 更新大屏显示
void updateBigScreenDisplay() {
    static unsigned long last_debug_time = 0;

    // 每5秒输出一次基本调试信息
    if (millis() - last_debug_time > 5000) {
        printf("=== Big Screen Update === millis: %lu, mode: %s\n",
               millis(), (current_display_mode == DISPLAY_MODE_BIG) ? "BIG" : "SMALL");
        last_debug_time = millis();
    }

    if (current_display_mode != DISPLAY_MODE_BIG) {
        if (millis() - last_debug_time > 2000) {
            printf("Not in BIG mode, returning\n");
        }
        return;
    }

    unsigned long now = millis();
    static unsigned long last_time_update = 0;
    static uint8_t last_minute = 255; // 初始化为无效值，确保首次更新
    static uint8_t last_weekday = 255;

    // 添加时间间隔检查的调试信息
    if (millis() - last_debug_time <= 100) { // 只在调试输出时显示
        printf("Time check: now=%lu, last_switch=%lu, interval=%lu\n",
               now, big_screen_last_switch, big_screen_switch_interval);
        printf("Time diff: %lu, need: %lu\n",
               now - big_screen_last_switch, big_screen_switch_interval);
        printf("Current state: %s\n",
               (big_screen_state == BIG_SCREEN_TIME) ? "TIME" : "WEEKDAY");
    }

    // 每秒检查一次时间更新（与小屏模式保持一致）
    if (now - last_time_update >= 1000) {
        // 使用独立的秒计数器（不修改原有结构体）
        static uint8_t big_screen_seconds = 0;

        big_screen_seconds++;
        if (big_screen_seconds >= 60) {
            big_screen_seconds = 0;
            time_info.minute++;
            if (time_info.minute >= 60) {
                time_info.minute = 0;
                time_info.hour++;
                if (time_info.hour >= 24) {
                    time_info.hour = 0;
                    // 新的一天，更新星期
                    time_info.weekday = (time_info.weekday + 1) % 7;
                    printf("New day! Weekday changed to: %d\n", time_info.weekday);
                }
                printf("Time updated: %02d:%02d\n", time_info.hour, time_info.minute);
            }
        }
        last_time_update = now;
    }

    // 检查是否需要重新绘制（时间或星期发生变化）
    bool need_redraw = false;
    if (time_info.minute != last_minute) {
        last_minute = time_info.minute;
        need_redraw = true;
        printf("Big screen time changed, need redraw\n");
    }
    if (time_info.weekday != last_weekday) {
        last_weekday = time_info.weekday;
        need_redraw = true;
        printf("Big screen weekday changed, need redraw\n");
    }

    // 检查是否需要切换显示内容
    bool state_changed = false;
    if (now - big_screen_last_switch >= big_screen_switch_interval) {
        BigScreenState old_state = big_screen_state;
        big_screen_state = (big_screen_state == BIG_SCREEN_TIME) ?
                          BIG_SCREEN_WEEKDAY : BIG_SCREEN_TIME;
        big_screen_last_switch = now;
        state_changed = true;

        printf("*** STATE SWITCH TRIGGERED ***\n");
        printf("Switch from %s to %s\n",
               (old_state == BIG_SCREEN_TIME) ? "TIME" : "WEEKDAY",
               (big_screen_state == BIG_SCREEN_TIME) ? "TIME" : "WEEKDAY");
        printf("New last_switch time: %lu\n", big_screen_last_switch);
        printf("Next switch in: %lu ms\n", big_screen_switch_interval);
    } else {
        // 添加倒计时调试信息
        unsigned long remaining = big_screen_switch_interval - (now - big_screen_last_switch);
        if (millis() - last_debug_time <= 100) {
            printf("No switch needed, remaining: %lu ms\n", remaining);
        }
    }

    // 只在需要时重新绘制（避免闪烁）
    if (need_redraw || state_changed) {
        if (big_screen_state == BIG_SCREEN_TIME) {
            drawBigScreenTime(state_changed);  // 状态切换时强制重绘
        } else {
            drawBigScreenWeekday(state_changed);  // 状态切换时强制重绘
        }
    }
}

// 绘制大屏时间（8x32数字字体，修正布局）
void drawBigScreenTime(bool force_redraw) {
    DisplayRegion* region = &display_regions[REGION_BIG_SCREEN];
    static uint8_t last_drawn_hour = 255;
    static uint8_t last_drawn_minute = 255;

    // 重绘条件：时间变化 OR 强制重绘（状态切换）
    bool time_changed = (time_info.hour != last_drawn_hour || time_info.minute != last_drawn_minute);
    bool need_redraw = time_changed || force_redraw;

    if (need_redraw) {
        // 智能清除区域（恢复GIF背景）
        EnhancedTextPixelMask::smartClearTextRegion(region->x1, region->y1,
                                                   region->x2 - region->x1 + 1,
                                                   region->y2 - region->y1 + 1);
        last_drawn_hour = time_info.hour;
        last_drawn_minute = time_info.minute;

        if (time_changed) {
            printf("Big screen time redraw: %02d:%02d (time changed)\n", time_info.hour, time_info.minute);
        } else if (force_redraw) {
            printf("Big screen time redraw: %02d:%02d (state switch forced)\n", time_info.hour, time_info.minute);
        }
    } else {
        // 时间没有变化且非强制重绘，不需要重绘
        printf("Big screen time: no redraw needed\n");
        return;
    }

    // 设置时间颜色
    region->text_color = big_screen_time_color;
    region->bg_color = big_screen_bg_color;

    int region_width = region->x2 - region->x1 + 1;  // 48像素
    int region_height = region->y2 - region->y1 + 1; // 32像素

    printf("Drawing big screen time in region %dx%d, region bounds: (%d,%d)-(%d,%d)\n",
           region_width, region_height, region->x1, region->y1, region->x2, region->y2);

    // 8x32字符，48像素宽度可以放6个字符（8×6=48）
    // 显示完整时间 HH:MM（5个字符：2+1+2）
    int char_width = 8;
    int char_height = 32;
    int total_width = 5 * char_width; // 40像素

    // 在48像素宽度内居中显示40像素的内容
    int start_x = (region_width - total_width) / 2; // 居中：(48-40)/2 = 4
    int start_y = 0; // 32像素高度正好匹配字符高度

    printf("Time layout: 5 chars × 8px = 40px total, centered at x=%d\n", start_x);

    // 绘制完整时间 HH:MM
    drawBig32x8TimeChar(region, time_info.hour / 10 + 1, start_x + 0 * char_width, start_y);  // 小时十位
    drawBig32x8TimeChar(region, time_info.hour % 10 + 1, start_x + 1 * char_width, start_y);  // 小时个位
    drawBig32x8TimeChar(region, 0, start_x + 2 * char_width, start_y);                        // 冒号
    drawBig32x8TimeChar(region, time_info.minute / 10 + 1, start_x + 3 * char_width, start_y); // 分钟十位
    drawBig32x8TimeChar(region, time_info.minute % 10 + 1, start_x + 4 * char_width, start_y); // 分钟个位

    printf("Big screen time drawn (full): %02d:%02d at offset (%d,%d)\n",
           time_info.hour, time_info.minute, start_x, start_y);
}

// 绘制大屏星期（32x16字体）
void drawBigScreenWeekday(bool force_redraw) {
    DisplayRegion* region = &display_regions[REGION_BIG_SCREEN];
    static uint8_t last_drawn_weekday = 255;
    static WeekdayLanguage last_drawn_language = (WeekdayLanguage)255;

    // 重绘条件：星期/语言变化 OR 强制重绘（状态切换）
    bool content_changed = (time_info.weekday != last_drawn_weekday || big_screen_language != last_drawn_language);
    bool need_redraw = content_changed || force_redraw;

    if (need_redraw) {
        // 智能清除区域（恢复GIF背景）
        EnhancedTextPixelMask::smartClearTextRegion(region->x1, region->y1,
                                                   region->x2 - region->x1 + 1,
                                                   region->y2 - region->y1 + 1);
        last_drawn_weekday = time_info.weekday;
        last_drawn_language = big_screen_language;

        if (content_changed) {
            printf("Big screen weekday redraw: %d, lang: %s (content changed)\n",
                   time_info.weekday, (big_screen_language == LANG_CHINESE) ? "CN" : "EN");
        } else if (force_redraw) {
            printf("Big screen weekday redraw: %d, lang: %s (state switch forced)\n",
                   time_info.weekday, (big_screen_language == LANG_CHINESE) ? "CN" : "EN");
        }
    } else {
        // 星期和语言都没有变化且非强制重绘，不需要重绘
        printf("Big screen weekday: no redraw needed\n");
        return;
    }

    // 设置星期颜色
    region->text_color = big_screen_weekday_color;
    region->bg_color = big_screen_bg_color;

    int region_width = region->x2 - region->x1 + 1;  // 48像素
    int region_height = region->y2 - region->y1 + 1; // 32像素

    if (big_screen_language == LANG_CHINESE) {
        // 中文显示：显示完整"星期X"（3个字符）
        int char_width = 16;  // 修正：每个字符16宽×32高
        int char_height = 32;
        int total_chars = 3;  // "星期X"三个字符
        int total_width = total_chars * char_width; // 48像素

        // 48像素宽度正好可以放3个16像素字符
        int start_x = 0; // 左对齐，正好填满48像素
        int start_y = 0; // 顶对齐，32像素高度正好匹配

        printf("Chinese layout: 3 chars × 16px = 48px total, at (%d,%d) in %dx%d region\n",
               start_x, start_y, region_width, region_height);

        // 显示完整"星期X"
        drawBig32x16ChineseChar(region, 0, start_x + 0 * char_width, start_y); // "星"
        drawBig32x16ChineseChar(region, 1, start_x + 1 * char_width, start_y); // "期"
        drawBig32x16ChineseChar(region, time_info.weekday + 2, start_x + 2 * char_width, start_y); // 数字部分

        const char* weekday_names[] = {"星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"};
        printf("Big screen weekday drawn (Chinese): %s at offset (%d,%d)\n",
               weekday_names[time_info.weekday], start_x, start_y);
    } else {
        // 英文显示：显示完整星期前三个字母（如"SAT"）
        int char_width = 16; // 英文字符宽度16像素
        int char_height = 32;
        int total_chars = 3; // 显示3个字符
        int total_width = total_chars * char_width; // 48像素

        // 48像素宽度正好可以放3个16像素字符
        int start_x = 0; // 左对齐，正好填满48像素
        int start_y = 0; // 顶对齐，32像素高度正好匹配

        printf("English layout: 3 chars × 16px = 48px total, at (%d,%d) in %dx%d region\n",
               start_x, start_y, region_width, region_height);

        drawBig32x16EnglishChars(region, time_info.weekday, start_x, start_y);

        const char* weekday_names[] = {"SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"};
        printf("Big screen weekday drawn (English): %s at offset (%d,%d)\n",
               weekday_names[time_info.weekday], start_x, start_y);
    }
}

// 绘制8x32数字字符（修正格式：8宽×32高）
void drawBig32x8TimeChar(DisplayRegion* region, uint8_t char_index, int x, int y) {
    if (!region->visible || !dma_display || char_index >= 11) return;

    const uint8_t* char_data = number_font[char_index];

    printf("Drawing time char[%d] at (%d,%d) - format: 8w×32h\n", char_index, x, y);

    // 数字字体格式：8宽×32高，每字节代表一行的8个像素
    // number_font[11][32]：每个字符32字节，每字节是一行
    for (int row = 0; row < 32; row++) {
        uint8_t row_data = pgm_read_byte(&char_data[row]); // 从Flash读取

        for (int bit = 0; bit < 8; bit++) {
            int pixel_x = region->x1 + x + bit;
            int pixel_y = region->y1 + y + row;

            // 边界检查
            if (pixel_x > region->x2 || pixel_y > region->y2 ||
                pixel_x < region->x1 || pixel_y < region->y1) {
                printf("Pixel (%d,%d) out of bounds [%d,%d]-[%d,%d]\n",
                       pixel_x, pixel_y, region->x1, region->y1, region->x2, region->y2);
                continue;
            }

            if (row_data & (0x01 << bit)) {
                EnhancedTextPixelMask::smartDrawTextPixel(pixel_x, pixel_y, region->text_color);
            }
            // 删除auto_clear分支，不再绘制背景像素
        }
    }

    printf("Time char[%d] drawn: 8×32 pixels at region offset (%d,%d)\n", char_index, x, y);
}

// 绘制32x32中文字符（缩放到16x32显示）- 已注释，改用32x16
/*
void drawBig32x32ChineseChar(DisplayRegion* region, uint8_t char_index, int x, int y) {
    if (!region->visible || !dma_display || char_index >= 9) return;

    const uint8_t* char_data = chinese_font[char_index];

    // 32x32字符缩放到16x32显示（水平缩放比例1:2）
    for (int row = 0; row < 32; row++) {
        for (int byte_idx = 0; byte_idx < 2; byte_idx++) { // 只取前2个字节（16位）
            uint8_t byte_data = char_data[row * 4 + byte_idx];

            for (int bit = 0; bit < 8; bit++) {
                int pixel_x = region->x1 + x + (byte_idx * 8) + bit;
                int pixel_y = region->y1 + y + row;

                // 边界检查
                if (pixel_x > region->x2 || pixel_y > region->y2 ||
                    pixel_x < region->x1 || pixel_y < region->y1) continue;

                if (byte_data & (0x01 << bit)) {
                    dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
                } else if (region->auto_clear) {
                    dma_display->drawPixel(pixel_x, pixel_y, region->bg_color);
                }
            }
        }
    }
}
*/

// 绘制32高×16宽中文字符（正确格式）
void drawBig32x16ChineseChar(DisplayRegion* region, uint8_t char_index, int x, int y) {
    if (!region->visible || !dma_display || char_index >= 9) return;

    const uint8_t* char_data = font_chinese[char_index];

    printf("Drawing Chinese char[%d] at (%d,%d) - format: 32h×16w\n", char_index, x, y);

    // 中文字体数据：font_chinese[9][72]
    // 格式：32高×16宽，每个字符72字节
    // 数据结构：32行，每行2字节（16位宽度），共64字节，剩余8字节可能是额外信息
    for (int row = 0; row < 32; row++) {
        // 每行2字节（16位宽度）
        int data_index = row * 2;
        if (data_index + 1 >= 72) break; // 安全检查，防止越界

        uint8_t left_byte = pgm_read_byte(&char_data[data_index]);     // 从Flash读取左半部分
        uint8_t right_byte = pgm_read_byte(&char_data[data_index + 1]); // 从Flash读取右半部分

        // 绘制左半部分（8位）
        for (int bit = 0; bit < 8; bit++) {
            int pixel_x = region->x1 + x + bit;
            int pixel_y = region->y1 + y + row;

            // 边界检查
            if (pixel_x > region->x2 || pixel_y > region->y2 ||
                pixel_x < region->x1 || pixel_y < region->y1) continue;

            if (left_byte & (0x01 << bit)) {
                EnhancedTextPixelMask::smartDrawTextPixel(pixel_x, pixel_y, region->text_color);
            }
            // 删除auto_clear分支，不再绘制背景像素
        }

        // 绘制右半部分（8位）
        for (int bit = 0; bit < 8; bit++) {
            int pixel_x = region->x1 + x + 8 + bit;
            int pixel_y = region->y1 + y + row;

            // 边界检查
            if (pixel_x > region->x2 || pixel_y > region->y2 ||
                pixel_x < region->x1 || pixel_y < region->y1) continue;

            if (right_byte & (0x01 << bit)) {
                EnhancedTextPixelMask::smartDrawTextPixel(pixel_x, pixel_y, region->text_color);
            }
            // 删除auto_clear分支，不再绘制背景像素
        }
    }

    printf("Chinese char[%d] drawn: 32h×16w pixels at region offset (%d,%d)\n",
           char_index, x, y);
}

// 绘制32高×16宽英文字符（正确格式）
void drawBig32x16EnglishChars(DisplayRegion* region, uint8_t weekday, int x, int y) {
    if (!region->visible || !dma_display || weekday > 6) return;

    printf("Drawing English weekday[%d] at (%d,%d) - format: 32h×16w\n", weekday, x, y);

    const char* weekday_names[] = {"SUNDAY", "MONDAY", "TUESDAY", "WEDNESDAY", "THURSDAY", "FRIDAY", "SATURDAY"};

    // 字符索引映射：每个星期对应3个字符
    // SUN: 0,1,2   MON: 3,4,5   TUE: 6,7,8   WED: 9,10,11   THU: 12,13,14   FRI: 15,16,17   SAT: 18,19,20
    uint8_t base_index = weekday * 3; // 每个星期3个字符

    printf("Drawing weekday %s (index %d), base_index=%d\n", weekday_names[weekday], weekday, base_index);

    // 绘制3个字符（前三个字母）
    for (int char_pos = 0; char_pos < 3; char_pos++) {
        uint8_t char_index = base_index + char_pos;

        if (char_index >= 21) break; // 安全检查

        // 计算字符位置：每个字符16像素宽，但区域只有48像素
        // 3个字符需要48像素，正好填满
        int char_x = x + char_pos * 16;

        printf("Drawing char[%d] at x=%d\n", char_index, char_x);
        const uint8_t* char_data = en_font[char_index];

        printf("Drawing char_index[%d] at char_x=%d, region bounds: (%d,%d)-(%d,%d)\n",
               char_index, char_x, region->x1, region->y1, region->x2, region->y2);

        // 英文字体格式：32高×16宽
        // en_font[21][64]：每个字符64字节
        // 正确的数据结构：32行，每行2字节（16位宽度）
        // 32行 × 2字节/行 = 64字节

        // 按照正确的32高×16宽格式绘制
        for (int row = 0; row < 32; row++) {
            // 数据存储方式：每行2字节连续存储
            uint8_t left_byte = pgm_read_byte(&char_data[row * 2]);     // 左半部分（8位）
            uint8_t right_byte = pgm_read_byte(&char_data[row * 2 + 1]); // 右半部分（8位）

            // 绘制左半部分（8位）
            for (int bit = 0; bit < 8; bit++) {
                int pixel_x = region->x1 + char_x + bit;
                int pixel_y = region->y1 + y + row;

                // 边界检查
                if (pixel_x > region->x2 || pixel_y > region->y2 ||
                    pixel_x < region->x1 || pixel_y < region->y1) continue;

                if (left_byte & (0x01 << bit)) {
                    EnhancedTextPixelMask::smartDrawTextPixel(pixel_x, pixel_y, region->text_color);
                }
                // 删除auto_clear分支，不再绘制背景像素
            }

            // 绘制右半部分（8位）
            for (int bit = 0; bit < 8; bit++) {
                int pixel_x = region->x1 + char_x + 8 + bit;
                int pixel_y = region->y1 + y + row;

                // 边界检查
                if (pixel_x > region->x2 || pixel_y > region->y2 ||
                    pixel_x < region->x1 || pixel_y < region->y1) continue;

                if (right_byte & (0x01 << bit)) {
                    EnhancedTextPixelMask::smartDrawTextPixel(pixel_x, pixel_y, region->text_color);
                }
                // 删除auto_clear分支，不再绘制背景像素
            }
        }

        printf("English char[%d] drawn: 32h×16w pixels at char_x=%d\n", char_index, char_x);
    }

    printf("English weekday drawn: %s (3 chars)\n", weekday_names[weekday]);
}

// ==================== 大屏配置函数 ====================

// 设置大屏切换间隔
void setBigScreenSwitchInterval(unsigned long interval_ms) {
    big_screen_switch_interval = interval_ms;
    printf("Big screen switch interval set to: %lu ms\n", interval_ms);
}

// 设置大屏语言
void setBigScreenLanguage(WeekdayLanguage language) {
    if (language < LANG_COUNT && language != big_screen_language) {
        big_screen_language = language;
        printf("Big screen language changed to: %d\n", language);

        // 只有在大屏模式且当前显示星期时才重绘
        if (current_display_mode == DISPLAY_MODE_BIG && big_screen_state == BIG_SCREEN_WEEKDAY) {
            drawBigScreenWeekday(true);  // 语言切换时强制重绘
        }
    }
}

// 设置大屏时间和星期
void setBigScreenTimeAndWeekday(uint8_t hour, uint8_t minute, uint8_t weekday, WeekdayLanguage language) {
    if (hour < 24 && minute < 60 && weekday <= 6 && language < LANG_COUNT) {
        time_info.hour = hour;
        time_info.minute = minute;
        time_info.weekday = weekday;
        big_screen_language = language;

        if (current_display_mode == DISPLAY_MODE_BIG) {
            updateBigScreenDisplay();
        }

        printf("Big screen time and weekday set: %02d:%02d, weekday=%d, lang=%d\n",
               hour, minute, weekday, language);
    }
}

// ==================== 大屏颜色控制函数 ====================

// 设置大屏时间颜色
void setBigScreenTimeColor(uint16_t color) {
    big_screen_time_color = color;
    if (current_display_mode == DISPLAY_MODE_BIG && big_screen_state == BIG_SCREEN_TIME) {
        updateBigScreenDisplay();
    }
    printf("Big screen time color set to: 0x%04X\n", color);
}

// 设置大屏星期颜色
void setBigScreenWeekdayColor(uint16_t color) {
    big_screen_weekday_color = color;
    if (current_display_mode == DISPLAY_MODE_BIG && big_screen_state == BIG_SCREEN_WEEKDAY) {
        updateBigScreenDisplay();
    }
    printf("Big screen weekday color set to: 0x%04X\n", color);
}

// 设置大屏背景颜色
void setBigScreenBackgroundColor(uint16_t color) {
    big_screen_bg_color = color;
    display_regions[REGION_BIG_SCREEN].bg_color = color;
    if (current_display_mode == DISPLAY_MODE_BIG) {
        updateBigScreenDisplay();
    }
    printf("Big screen background color set to: 0x%04X\n", color);
}

// ==================== 简洁封装函数 ====================

/*
// 一键切换到大屏模式并设置参数 - 已注释，使用参数化测试函数
void switchToBigScreen(uint8_t hour, uint8_t minute, uint8_t weekday, WeekdayLanguage language,
                      unsigned long switch_interval, uint16_t time_color, uint16_t weekday_color) {
    printf("=== Switching to Big Screen Mode ===\n");

    // 设置所有参数
    setBigScreenTimeAndWeekday(hour, minute, weekday, language);
    setBigScreenSwitchInterval(switch_interval);
    setBigScreenTimeColor(time_color);
    setBigScreenWeekdayColor(weekday_color);

    // 切换到大屏模式
    setDisplayMode(DISPLAY_MODE_BIG);

    printf("Big screen mode activated successfully\n");
}

// 一键切换回小屏模式 - 已注释
void switchToSmallScreen() {
    printf("=== Switching to Small Screen Mode ===\n");
    setDisplayMode(DISPLAY_MODE_SMALL);
    printf("Small screen mode activated successfully\n");
}
*/

// 快速设置大屏颜色主题
void setBigScreenColorTheme(uint16_t time_color, uint16_t weekday_color, uint16_t bg_color) {
    setBigScreenTimeColor(time_color);
    setBigScreenWeekdayColor(weekday_color);
    setBigScreenBackgroundColor(bg_color);
    printf("Big screen color theme applied\n");
}

// ==================== 测试函数 ====================

// 启动大屏模式循环演示
void testBigScreenMode(uint8_t hour, uint8_t minute, uint8_t weekday, WeekdayLanguage language) {
    printf("\n==================== BIG SCREEN MODE DEMO STARTED ====================\n");
    printf("Demo parameters: %02d:%02d, weekday=%d, language=%s\n",
           hour, minute, weekday, (language == LANG_CHINESE) ? "CHINESE" : "ENGLISH");

    // 设置演示参数
    time_info.hour = hour;
    time_info.minute = minute;
    time_info.weekday = weekday;
    setBigScreenLanguage(language);

    printf("Current mode: %s\n", (current_display_mode == DISPLAY_MODE_SMALL) ? "SMALL" : "BIG");
    printf("Free heap: %d bytes\n", ESP.getFreeHeap());

    // 修复：先设置参数，再切换模式
    printf("\nSetting up big screen parameters...\n");

    // 1. 先设置切换间隔
    setBigScreenSwitchInterval(5000);
    printf("Switch interval set to: 5 seconds\n");

    // 2. 设置初始状态和时间（强制立即触发切换）
    big_screen_state = BIG_SCREEN_TIME; // 从时间显示开始
    big_screen_last_switch = millis() - 5000; // 设置为5秒前，立即触发切换
    printf("Initial state: TIME, last_switch set to trigger immediate switch\n");
    printf("Current millis: %lu, last_switch: %lu\n", millis(), big_screen_last_switch);

    // 3. 最后切换到大屏模式
    printf("\nSwitching to big screen mode...\n");
    setDisplayMode(DISPLAY_MODE_BIG);
    printf("Mode switched to: %s\n", (current_display_mode == DISPLAY_MODE_BIG) ? "BIG" : "SMALL");

    // 启动大屏模式（让系统自动循环切换）
    printf("\nStarting big screen demo mode...\n");
    printf("Will automatically switch between TIME and WEEKDAY every 5 seconds\n");
    printf("Demo will run continuously - system will handle automatic switching\n");

    printf("Starting with TIME display...\n");

    // 不使用delay()，不阻塞主循环
    // 让updateBigScreenDisplay()自动处理切换
    printf("\n==================== DEMO RUNNING ====================\n");
    printf("Big screen demo is now running!\n");
    printf("- TIME display: %02d:%02d\n", time_info.hour, time_info.minute);
    printf("- WEEKDAY display: %s\n", (language == LANG_CHINESE) ? "中文星期" : "English weekday");
    printf("- Auto-switching every 5 seconds\n");
    printf("- Time will auto-update every minute\n");
    printf("Demo will continue until manually stopped...\n");

    // delay(3000);

    // // 测试另一种语言
    // WeekdayLanguage other_language = (language == LANG_CHINESE) ? LANG_ENGLISH : LANG_CHINESE;
    // printf("\nTesting other language: %s\n", (other_language == LANG_CHINESE) ? "CHINESE" : "ENGLISH");
    // setBigScreenLanguage(other_language);
    // drawBigScreenWeekday();
    // printf("Other language display test completed\n");

    // delay(3000);

    // // 切换回小屏模式
    // printf("\nSwitching back to small screen mode...\n");
    // setDisplayMode(DISPLAY_MODE_SMALL);
    // printf("Mode switched back to: %s\n", (current_display_mode == DISPLAY_MODE_SMALL) ? "SMALL" : "BIG");

    // printf("\n==================== TEST COMPLETED ====================\n");
    // printf("Final mode: %s\n", (current_display_mode == DISPLAY_MODE_SMALL) ? "SMALL" : "BIG");
    // printf("Free heap: %d bytes\n", ESP.getFreeHeap());
}

// ==================== EnhancedTextPixelMask类实现 ====================

// ==================== 系统管理函数 ====================
bool EnhancedTextPixelMask::initSystem() {
    if (system_initialized) {
        printf("Enhanced mask system already initialized\n");
        return true;
    }

    // 检查可用内存
    size_t free_heap = ESP.getFreeHeap();
    size_t required_memory = (2048 * sizeof(bool)) + (2048 * sizeof(uint16_t)); // 6KB

    if (free_heap < required_memory + 10240) { // 保留10KB安全余量
        printf("ERROR: Insufficient memory. Free: %d, Required: %d\n",
               free_heap, required_memory);
        return false;
    }

    // 分配内存
    text_mask = (bool*)malloc(2048 * sizeof(bool));
    gif_backup = (uint16_t*)malloc(2048 * sizeof(uint16_t));

    if (!text_mask || !gif_backup) {
        printf("ERROR: Memory allocation failed\n");
        freeSystem();
        return false;
    }

    // 初始化缓冲区
    initializeBuffers();

    system_initialized = true;
    printf("Enhanced mask system initialized successfully. Memory used: %d bytes\n",
           required_memory);
    printf("Free heap after initialization: %d bytes\n", ESP.getFreeHeap());

    return true;
}

void EnhancedTextPixelMask::freeSystem() {
    if (text_mask) {
        free(text_mask);
        text_mask = nullptr;
    }
    if (gif_backup) {
        free(gif_backup);
        gif_backup = nullptr;
    }

    system_initialized = false;
    printf("Enhanced mask system freed\n");
}

bool EnhancedTextPixelMask::isSystemReady() {
    return system_initialized && text_mask && gif_backup;
}

void EnhancedTextPixelMask::initializeBuffers() {
    // 初始化文字遮罩为false（无文字）
    memset(text_mask, false, 2048 * sizeof(bool));

    // 初始化GIF备份为默认背景色
    for (int i = 0; i < 2048; i++) {
        gif_backup[i] = default_bg_color;
    }

    printf("Buffers initialized with default values\n");
}

// ==================== GIF像素管理函数 ====================
void EnhancedTextPixelMask::backupGifPixel(int16_t x, int16_t y, uint16_t color) {
    if (!isSystemReady() || !isValidCoordinate(x, y)) return;

    int index = getPixelIndex(x, y);
    gif_backup[index] = color;

    // 如果该像素没有被文字占用，直接显示GIF
    if (!text_mask[index]) {
        dma_display->drawPixel(x, y, color);
    }
}

uint16_t EnhancedTextPixelMask::getGifPixel(int16_t x, int16_t y) {
    if (!isSystemReady() || !isValidCoordinate(x, y)) {
        return default_bg_color;
    }

    int index = getPixelIndex(x, y);
    return gif_backup[index];
}

void EnhancedTextPixelMask::updateGifRegion(int16_t x, int16_t y, int16_t w, int16_t h, uint16_t color) {
    if (!isSystemReady()) return;

    for (int16_t py = y; py < y + h && py < SCREEN_HEIGHT; py++) {
        if (py < 0) continue;
        for (int16_t px = x; px < x + w && px < SCREEN_WIDTH; px++) {
            if (px >= 0) {
                backupGifPixel(px, py, color);
            }
        }
    }
}

// ==================== 智能显示管理函数 ====================
void EnhancedTextPixelMask::smartDrawGifPixel(int16_t x, int16_t y, uint16_t color) {
    if (!isSystemReady()) {
        // 系统未就绪时，回退到传统方式
        dma_display->drawPixel(x, y, color);
        return;
    }

    // 备份GIF像素并智能显示
    backupGifPixel(x, y, color);
}

void EnhancedTextPixelMask::smartDrawTextPixel(int16_t x, int16_t y, uint16_t color) {
    if (!isSystemReady()) {
        // 系统未就绪时，回退到传统方式
        dma_display->drawPixel(x, y, color);
        return;
    }

    if (!isValidCoordinate(x, y)) return;

    int index = getPixelIndex(x, y);

    // 设置文字遮罩
    text_mask[index] = true;

    // 绘制文字像素（总是显示在最前面）
    dma_display->drawPixel(x, y, color);
}

void EnhancedTextPixelMask::smartClearTextRegion(int16_t x, int16_t y, int16_t w, int16_t h) {
    if (!isSystemReady()) return;

    for (int16_t py = y; py < y + h && py < SCREEN_HEIGHT; py++) {
        if (py < 0) continue;
        for (int16_t px = x; px < x + w && px < SCREEN_WIDTH; px++) {
            if (px < 0) continue;

            int index = getPixelIndex(px, py);

            // 只处理被文字占用的像素
            if (text_mask[index]) {
                // 恢复GIF像素
                uint16_t gif_color = gif_backup[index];
                dma_display->drawPixel(px, py, gif_color);

                // 清除文字遮罩
                text_mask[index] = false;
            }
        }
    }
}

void EnhancedTextPixelMask::restoreGifRegion(int16_t x, int16_t y, int16_t w, int16_t h) {
    if (!isSystemReady()) return;

    for (int16_t py = y; py < y + h && py < SCREEN_HEIGHT; py++) {
        if (py < 0) continue;
        for (int16_t px = x; px < x + w && px < SCREEN_WIDTH; px++) {
            if (px < 0) continue;

            int index = getPixelIndex(px, py);

            // 恢复GIF像素（无论是否被文字占用）
            uint16_t gif_color = gif_backup[index];
            dma_display->drawPixel(px, py, gif_color);

            // 清除文字遮罩
            text_mask[index] = false;
        }
    }
}

// ==================== 基础功能函数 ====================
bool EnhancedTextPixelMask::isPixelOccupiedByText(int16_t x, int16_t y) {
    if (!isSystemReady() || !isValidCoordinate(x, y)) {
        return false;
    }

    int index = getPixelIndex(x, y);
    return text_mask[index];
}

void EnhancedTextPixelMask::setTextPixel(int16_t x, int16_t y, bool occupied) {
    if (!isSystemReady() || !isValidCoordinate(x, y)) return;

    int index = getPixelIndex(x, y);
    text_mask[index] = occupied;
}

void EnhancedTextPixelMask::clearTextRegion(int16_t x, int16_t y, int16_t w, int16_t h) {
    if (!isSystemReady()) return;

    for (int16_t py = y; py < y + h && py < SCREEN_HEIGHT; py++) {
        if (py < 0) continue;
        for (int16_t px = x; px < x + w && px < SCREEN_WIDTH; px++) {
            if (px >= 0) {
                int index = getPixelIndex(px, py);
                text_mask[index] = false;
            }
        }
    }
}

void EnhancedTextPixelMask::clearGifRegion(int16_t x1, int16_t y1, int16_t x2, int16_t y2, uint16_t bg_color) {
    if (!isSystemReady()) return;//return system_initialized && text_mask && gif_backup;

    printf("Clearing GIF region: (%d,%d) to (%d,%d)\n", x1, y1, x2, y2);

    for (int16_t py = y1; py <= y2 && py < SCREEN_HEIGHT; py++) {
        if (py < 0) continue;
        for (int16_t px = x1; px <= x2 && px < SCREEN_WIDTH; px++) {
            if (px < 0) continue;

            int index = getPixelIndex(px, py);//y * SCREEN_WIDTH + x;

            // 清除GIF缓冲区数据
            gif_backup[index] = bg_color;

            // 如果该位置没有文字，立即更新显示
            if (!text_mask[index]) {
                dma_display->drawPixel(px, py, bg_color);
            }
        }
    }

    printf("GIF region cleared with background color 0x%04X\n", bg_color);
}

// ==================== 内部辅助函数 ====================
inline int EnhancedTextPixelMask::getPixelIndex(int16_t x, int16_t y) {
    return y * SCREEN_WIDTH + x;
}

inline bool EnhancedTextPixelMask::isValidCoordinate(int16_t x, int16_t y) {
    return (x >= 0 && y >= 0 && x < SCREEN_WIDTH && y < SCREEN_HEIGHT);
}

// ==================== 调试和监控函数 ====================
void EnhancedTextPixelMask::printSystemStatus() {
    if (!isSystemReady()) {
        printf("Enhanced mask system: NOT READY\n");
        return;
    }

    // 统计文字像素数量
    int text_pixel_count = 0;
    for (int i = 0; i < 2048; i++) {
        if (text_mask[i]) text_pixel_count++;
    }

    printf("=== Enhanced Mask System Status ===\n");
    printf("System Ready: %s\n", system_initialized ? "YES" : "NO");
    printf("Memory Usage: %d bytes\n", getMemoryUsage());
    printf("Text Pixels: %d / 2048\n", text_pixel_count);
    printf("Free Heap: %d bytes\n", ESP.getFreeHeap());
    printf("=====================================\n");
}

size_t EnhancedTextPixelMask::getMemoryUsage() {
    if (!isSystemReady()) return 0;
    return (2048 * sizeof(bool)) + (2048 * sizeof(uint16_t));
}

void EnhancedTextPixelMask::dumpRegionInfo(int16_t x, int16_t y, int16_t w, int16_t h) {
    if (!isSystemReady()) return;

    printf("=== Region Info: (%d,%d) %dx%d ===\n", x, y, w, h);

    for (int16_t py = y; py < y + h && py < SCREEN_HEIGHT; py++) {
        if (py < 0) continue;

        printf("Row %2d: ", py);
        for (int16_t px = x; px < x + w && px < SCREEN_WIDTH; px++) {
            if (px < 0) continue;

            int index = getPixelIndex(px, py);
            printf("%c", text_mask[index] ? 'T' : '.');
        }
        printf("\n");
    }
    printf("T=Text, .=GIF/Background\n");
}



// 分阶段进行GIF切换，避免内存冲突
void smoothGifSwitch(const char* new_gif) {
    printf("Starting smooth GIF switch...\n");
    
    // 阶段1：临时释放叠层系统内存
    EnhancedTextPixelMask::freeSystem();
    delay(100);  // 等待内存释放
    
    // 阶段2：关闭旧GIF
    gif.close();
    delay(100);  // 等待GIF内存释放
    
    // 阶段3：强制垃圾回收（如果ESP32支持）
    // ESP.gc();  // 某些版本支持
    
    // 阶段4：打开新GIF
    if (playGIF(new_gif)) {
        printf("New GIF loaded successfully\n");
    }
    
    // 阶段5：重新初始化叠层系统
    delay(200);  // 等待GIF稳定
    EnhancedTextPixelMask::initSystem();
    
    printf("Smooth GIF switch completed\n");
}


