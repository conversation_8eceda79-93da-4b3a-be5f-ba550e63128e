# 字体显示原理与多语言适配指南

## 概述

本文档详细解释三分区时钟界面中字体显示的工作原理，包括区域内坐标系统、字符定位机制、以及如何适配不同尺寸的字体和多语言支持。

## 字体显示原理深度解析

### 1. 区域坐标系统

#### 1.1 双重坐标系统
系统采用了**双重坐标系统**：
- **全局坐标系**：以整个LED屏幕为基准 (0,0) 到 (63,31)
- **区域坐标系**：以每个区域左上角为基准 (0,0) 到 (区域宽度-1, 区域高度-1)

```
全局坐标系 (64x32屏幕)
┌─────────────────────────────────────────────────────────────┐
│ (0,0)                                              (63,0)   │
│                                                             │
│  区域坐标系 (时间区域 16,0-63,15)                            │
│  ┌─────────────────────────────────────────────────┐        │
│  │ (0,0)区域内                              (47,0) │        │
│  │                                                 │        │
│  │     字符绘制基准点                               │        │
│  │     ↓                                           │        │
│  │   (4,0) ← start_x, start_y                     │        │
│  │                                        (47,15) │        │
│  └─────────────────────────────────────────────────┘        │
│ (0,31)                                             (63,31)  │
└─────────────────────────────────────────────────────────────┘
```

#### 1.2 坐标转换机制
```cpp
// 在drawCharToRegion函数中的关键转换
int pixel_x = region->x1 + x + col;  // 区域坐标转全局坐标
int pixel_y = region->y1 + y + row;  // 区域坐标转全局坐标

// 其中：
// region->x1, region->y1 = 区域在全局坐标系中的左上角
// x, y = 字符在区域内的起始位置
// col, row = 字符内部的像素偏移
```

### 2. 字符定位机制详解

#### 2.1 当前的字符定位逻辑
```cpp
void drawTimeToRegion(DisplayRegion* region, uint8_t hour, uint8_t minute) {
    // 清除区域
    clearRegion(region);
    
    // 计算显示位置 (当前是固定位置，不是真正的居中)
    int8_t start_x = 4;  // 左边留4像素边距
    int8_t start_y = 0;  // 顶部对齐
    
    // 字符排列：HH:MM
    // 位置计算：
    // 小时十位: start_x + 0 * FONT_WIDTH = 4
    // 小时个位: start_x + 1 * FONT_WIDTH = 12  
    // 冒号:     start_x + 2 * FONT_WIDTH = 20
    // 分钟十位: start_x + 3 * FONT_WIDTH = 28
    // 分钟个位: start_x + 4 * FONT_WIDTH = 36
}
```

#### 2.2 光标机制的真实作用
**重要发现**：当前代码中的`cursor_x`和`cursor_y`实际上**没有被使用**！

```cpp
typedef struct {
    // ...
    int8_t cursor_x;          // 在区域内的光标X位置 (未使用)
    int8_t cursor_y;          // 在区域内的光标Y位置 (未使用)
    // ...
} DisplayRegion;
```

字符位置完全由`drawCharToRegion`函数的`x, y`参数直接指定，而不是基于光标位置。

### 3. 字符绘制的具体流程

#### 3.1 8x16字体绘制流程
```cpp
void drawCharToRegion(DisplayRegion* region, uint8_t char_index, int8_t x, int8_t y) {
    const uint8_t* char_data = font_data[char_index];  // 获取字符点阵数据
    
    for (int row = 0; row < FONT_HEIGHT; row++) {      // 16行
        uint8_t line_data = char_data[row];            // 当前行的8位数据
        for (int col = 0; col < FONT_WIDTH; col++) {   // 8列
            // 坐标转换：区域坐标 → 全局坐标
            int pixel_x = region->x1 + x + col;
            int pixel_y = region->y1 + y + row;
            
            // 边界检查
            if (pixel_x > region->x2 || pixel_y > region->y2) continue;
            
            // 位检查和像素绘制
            if (line_data & (0x01 << col)) {
                dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
            }
        }
    }
}
```

#### 3.2 16x16字体绘制流程
```cpp
void drawWeekdayCharToRegion(DisplayRegion* region, uint8_t char_index, int8_t x, int8_t y) {
    const uint8_t* char_data = weekday_font_data[char_index];
    
    for (int row = 0; row < 16; row++) {
        // 每行2个字节 (16位)
        uint8_t byte1 = char_data[row * 2];     // 低8位
        uint8_t byte2 = char_data[row * 2 + 1]; // 高8位
        
        // 绘制低8位
        for (int col = 0; col < 8; col++) {
            int pixel_x = region->x1 + x + col;
            int pixel_y = region->y1 + y + row;
            if (byte1 & (0x01 << col)) {
                dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
            }
        }
        
        // 绘制高8位
        for (int col = 0; col < 8; col++) {
            int pixel_x = region->x1 + x + col + 8;  // 注意：+8偏移
            int pixel_y = region->y1 + y + row;
            if (byte2 & (0x01 << col)) {
                dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
            }
        }
    }
}
```

## 32x32字体适配方案

### 1. 字体数据结构设计

#### 1.1 32x32字体数据格式
```cpp
// 32x32字体需要每行4个字节 (32位)
const unsigned char large_font_data[CHAR_COUNT][128] = {  // 32行 × 4字节 = 128字节
    { // 字符数据
        // 第1行 (4字节)
        0x00, 0x00, 0x00, 0x00,
        // 第2行 (4字节)  
        0x00, 0x00, 0x00, 0x00,
        // ... 共32行
    }
};
```

#### 1.2 字体尺寸常量定义
```cpp
// 在config.h或gif_player.h中添加
#define LARGE_FONT_WIDTH     32
#define LARGE_FONT_HEIGHT    32
#define LARGE_FONT_BYTES_PER_ROW 4  // 32位 = 4字节
```

### 2. 32x32字体绘制函数

```cpp
void drawLargeCharToRegion(DisplayRegion* region, uint8_t char_index, int8_t x, int8_t y) {
    if (!region->visible || !dma_display || char_index >= LARGE_CHAR_COUNT) return;
    
    const uint8_t* char_data = large_font_data[char_index];
    
    for (int row = 0; row < LARGE_FONT_HEIGHT; row++) {
        // 每行4个字节 (32位)
        for (int byte_idx = 0; byte_idx < LARGE_FONT_BYTES_PER_ROW; byte_idx++) {
            uint8_t byte_data = char_data[row * LARGE_FONT_BYTES_PER_ROW + byte_idx];
            
            // 绘制当前字节的8位
            for (int bit = 0; bit < 8; bit++) {
                int pixel_x = region->x1 + x + (byte_idx * 8) + bit;
                int pixel_y = region->y1 + y + row;
                
                // 边界检查
                if (pixel_x > region->x2 || pixel_y > region->y2 ||
                    pixel_x < region->x1 || pixel_y < region->y1) continue;
                
                // 像素绘制
                if (byte_data & (0x01 << bit)) {
                    dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
                } else if (region->auto_clear) {
                    dma_display->drawPixel(pixel_x, pixel_y, region->bg_color);
                }
            }
        }
    }
}
```

### 3. 区域内字符精确定位

#### 3.1 居中显示计算
```cpp
void drawLargeTimeToRegion(DisplayRegion* region, uint8_t hour, uint8_t minute) {
    clearRegion(region);
    
    // 计算区域尺寸
    int region_width = region->x2 - region->x1 + 1;
    int region_height = region->y2 - region->y1 + 1;
    
    // 计算时间显示总宽度 (HH:MM = 5个字符)
    int total_width = 5 * LARGE_FONT_WIDTH;
    
    // 居中计算
    int8_t start_x = (region_width - total_width) / 2;
    int8_t start_y = (region_height - LARGE_FONT_HEIGHT) / 2;
    
    // 确保不超出边界
    if (start_x < 0) start_x = 0;
    if (start_y < 0) start_y = 0;
    
    // 绘制字符
    drawLargeCharToRegion(region, hour/10 + 1, start_x + 0 * LARGE_FONT_WIDTH, start_y);
    drawLargeCharToRegion(region, hour%10 + 1, start_x + 1 * LARGE_FONT_WIDTH, start_y);
    drawLargeCharToRegion(region, 0, start_x + 2 * LARGE_FONT_WIDTH, start_y);  // 冒号
    drawLargeCharToRegion(region, minute/10 + 1, start_x + 3 * LARGE_FONT_WIDTH, start_y);
    drawLargeCharToRegion(region, minute%10 + 1, start_x + 4 * LARGE_FONT_WIDTH, start_y);
}
```

#### 3.2 自定义位置显示
```cpp
// 在区域内任意位置放置字符的通用函数
void drawCharAtPosition(DisplayRegion* region, uint8_t char_index, 
                       int8_t x_offset, int8_t y_offset) {
    // x_offset, y_offset 是相对于区域左上角的偏移量
    drawLargeCharToRegion(region, char_index, x_offset, y_offset);
}

// 使用示例：在区域右下角显示字符
void drawCharAtBottomRight(DisplayRegion* region, uint8_t char_index) {
    int region_width = region->x2 - region->x1 + 1;
    int region_height = region->y2 - region->y1 + 1;
    
    int8_t x = region_width - LARGE_FONT_WIDTH;
    int8_t y = region_height - LARGE_FONT_HEIGHT;
    
    drawCharAtPosition(region, char_index, x, y);
}
```

## 多语言字体适配方案

### 1. 多语言字体系统架构

#### 1.1 语言枚举定义
```cpp
typedef enum {
    LANG_CHINESE = 0,
    LANG_ENGLISH = 1,
    LANG_JAPANESE = 2,
    LANG_KOREAN = 3,
    LANG_ARABIC = 4,
    LANG_COUNT
} Language;
```

#### 1.2 多语言字体数据结构
```cpp
// 不同语言的字体数据
typedef struct {
    const unsigned char* font_data;
    uint8_t font_width;
    uint8_t font_height;
    uint8_t bytes_per_row;
    uint8_t char_count;
} FontInfo;

// 字体数据库
const FontInfo language_fonts[LANG_COUNT] = {
    // 中文字体 (16x16)
    {
        .font_data = chinese_font_data,
        .font_width = 16,
        .font_height = 16,
        .bytes_per_row = 2,
        .char_count = 100
    },
    // 英文字体 (8x16)
    {
        .font_data = english_font_data,
        .font_width = 8,
        .font_height = 16,
        .bytes_per_row = 1,
        .char_count = 95
    },
    // 日文字体 (16x16)
    {
        .font_data = japanese_font_data,
        .font_width = 16,
        .font_height = 16,
        .bytes_per_row = 2,
        .char_count = 200
    }
    // ... 其他语言
};
```

### 2. 通用字符绘制函数

#### 2.1 多语言字符绘制引擎
```cpp
void drawMultiLangChar(DisplayRegion* region, Language lang, uint8_t char_index, 
                      int8_t x, int8_t y) {
    if (lang >= LANG_COUNT) return;
    
    const FontInfo* font = &language_fonts[lang];
    const uint8_t* char_data = font->font_data + (char_index * font->font_height * font->bytes_per_row);
    
    for (int row = 0; row < font->font_height; row++) {
        for (int byte_idx = 0; byte_idx < font->bytes_per_row; byte_idx++) {
            uint8_t byte_data = char_data[row * font->bytes_per_row + byte_idx];
            
            for (int bit = 0; bit < 8; bit++) {
                int pixel_x = region->x1 + x + (byte_idx * 8) + bit;
                int pixel_y = region->y1 + y + row;
                
                // 边界检查
                if (pixel_x > region->x2 || pixel_y > region->y2 ||
                    pixel_x < region->x1 || pixel_y < region->y1) continue;
                
                // 像素绘制
                if (byte_data & (0x01 << bit)) {
                    dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
                } else if (region->auto_clear) {
                    dma_display->drawPixel(pixel_x, pixel_y, region->bg_color);
                }
            }
        }
    }
}
```

#### 2.2 多语言文本绘制
```cpp
void drawMultiLangText(DisplayRegion* region, Language lang, const uint8_t* text, 
                      uint8_t text_length, int8_t start_x, int8_t start_y) {
    const FontInfo* font = &language_fonts[lang];
    int8_t current_x = start_x;
    
    for (uint8_t i = 0; i < text_length; i++) {
        drawMultiLangChar(region, lang, text[i], current_x, start_y);
        current_x += font->font_width;
        
        // 检查是否需要换行
        if (current_x + font->font_width > (region->x2 - region->x1 + 1)) {
            current_x = start_x;
            start_y += font->font_height;
        }
    }
}
```

### 3. 字符编码映射系统

#### 3.1 Unicode到字体索引映射
```cpp
// Unicode字符到字体数组索引的映射表
typedef struct {
    uint16_t unicode;
    uint8_t font_index;
} CharMapping;

// 中文字符映射表示例
const CharMapping chinese_char_map[] = {
    {0x4E00, 0},  // "一" -> 索引0
    {0x4E8C, 1},  // "二" -> 索引1
    {0x4E09, 2},  // "三" -> 索引2
    // ... 更多映射
};

// 查找字符索引
uint8_t getCharIndex(Language lang, uint16_t unicode) {
    switch (lang) {
        case LANG_CHINESE:
            for (int i = 0; i < sizeof(chinese_char_map)/sizeof(CharMapping); i++) {
                if (chinese_char_map[i].unicode == unicode) {
                    return chinese_char_map[i].font_index;
                }
            }
            break;
        // ... 其他语言
    }
    return 0; // 默认字符
}
```

## 代码修改要点

### 1. 需要修改的文件和位置

#### 1.1 gif_player.h 修改点
```cpp
// 添加新的字体尺寸定义
#define LARGE_FONT_WIDTH     32
#define LARGE_FONT_HEIGHT    32

// 添加语言枚举
typedef enum { /* 语言定义 */ } Language;

// 添加字体信息结构
typedef struct { /* FontInfo定义 */ } FontInfo;

// 添加新函数声明
void drawLargeCharToRegion(DisplayRegion* region, uint8_t char_index, int8_t x, int8_t y);
void drawMultiLangChar(DisplayRegion* region, Language lang, uint8_t char_index, int8_t x, int8_t y);
```

#### 1.2 gif_player.cpp 修改点
```cpp
// 添加大字体数据数组
const unsigned char large_font_data[CHAR_COUNT][128] = { /* 数据 */ };

// 添加多语言字体数据
const FontInfo language_fonts[LANG_COUNT] = { /* 字体信息 */ };

// 实现新的绘制函数
void drawLargeCharToRegion() { /* 实现 */ }
void drawMultiLangChar() { /* 实现 */ }
```

### 2. 为什么需要这些修改

#### 2.1 数据结构扩展原因
- **字体尺寸变化**：从8x16扩展到32x32，数据量增加16倍
- **存储格式变化**：每行从1字节扩展到4字节
- **多语言支持**：不同语言字符集大小和编码方式不同

#### 2.2 函数接口扩展原因
- **绘制算法差异**：不同尺寸字体的位操作和坐标计算不同
- **语言切换需求**：需要运行时动态选择字体和字符集
- **向后兼容性**：保持现有8x16字体功能不变

### 3. 渐进式实现建议

#### 3.1 第一阶段：32x32字体支持
1. 添加大字体数据结构
2. 实现`drawLargeCharToRegion`函数
3. 修改时间显示函数使用大字体
4. 测试基本显示功能

#### 3.2 第二阶段：多语言框架
1. 设计语言枚举和字体信息结构
2. 实现通用字符绘制引擎
3. 添加字符编码映射系统
4. 实现语言切换功能

#### 3.3 第三阶段：完整集成
1. 整合所有字体系统
2. 优化内存使用和性能
3. 添加字体缓存机制
4. 完善错误处理和边界检查

## 总结

### 字符定位基准
- **区域左上角**：所有字符位置都相对于区域的左上角 (region->x1, region->y1)
- **直接坐标**：通过`drawCharToRegion`的x, y参数直接指定位置
- **无光标机制**：当前系统不使用cursor_x/cursor_y，位置完全由函数参数控制

### 扩展能力
- **任意尺寸支持**：通过修改字体数据结构和绘制算法，可支持任意尺寸字体
- **灵活定位**：可在区域内任意位置放置字符，支持居中、对齐等各种布局
- **多语言兼容**：通过统一的字体系统架构，可同时支持多种语言和字符集

## 实际应用示例

### 1. 英文星期显示已集成示例

#### 1.1 新增的多语言支持
系统现已支持中英文星期显示切换：

```cpp
// 语言枚举
typedef enum {
    LANG_CHINESE = 0,
    LANG_ENGLISH = 1,
    LANG_COUNT
} WeekdayLanguage;

// 使用示例
setWeekdayWithLanguage(1, LANG_ENGLISH);  // 显示 "MON"
setWeekdayWithLanguage(1, LANG_CHINESE);  // 显示 "星期一"
```

#### 1.2 英文星期字体数据结构
```cpp
// 英文星期字体数据 (8x16点阵，每个星期显示前3个字母)
const unsigned char english_weekday_font_data[7][48] = {
    { // SUNDAY (SUN) - 3个字符 × 16字节 = 48字节
        // S字符数据 (16字节)
        0x00,0x00,0x00,0x3C,0x66,0x62,0x06,0x0C,
        0x38,0x60,0x42,0x42,0x66,0x3C,0x00,0x00,
        // U字符数据 (16字节)
        0x00,0x00,0x00,0x42,0x42,0x42,0x42,0x42,
        0x42,0x42,0x42,0x42,0x66,0x3C,0x00,0x00,
        // N字符数据 (16字节)
        0x00,0x00,0x00,0x42,0x46,0x46,0x4E,0x4A,
        0x5A,0x52,0x72,0x62,0x62,0x62,0x00,0x00
    },
    // ... 其他6天的数据
};
```

### 2. 32x32字体全屏显示示例

#### 2.1 全屏时钟显示设计
```cpp
// 全屏32x32字体时钟显示 (适用于128x64或更大屏幕)
void setupFullScreenClock() {
    // 重新定义区域为全屏时钟显示
    // 假设128x64屏幕，显示 HH:MM 格式

    // 计算居中位置
    int screen_width = 128;
    int screen_height = 64;
    int time_width = 5 * 32;  // HH:MM = 5个字符 × 32像素
    int time_height = 32;

    int start_x = (screen_width - time_width) / 2;   // 水平居中
    int start_y = (screen_height - time_height) / 2; // 垂直居中

    // 定义全屏时钟区域
    initDisplayRegion(&display_regions[REGION_TIME],
                     start_x, start_y,
                     start_x + time_width - 1, start_y + time_height - 1,
                     COLOR_RED, COLOR_BLACK);
}

// 32x32字体时钟绘制函数
void drawFullScreenTime(uint8_t hour, uint8_t minute) {
    DisplayRegion* region = &display_regions[REGION_TIME];
    clearRegion(region);

    // 在区域内绘制大字体时间
    int char_x = 0;  // 相对于区域的X位置

    // 绘制小时
    drawLarge32CharToRegion(region, hour/10 + 1, char_x, 0);
    char_x += 32;
    drawLarge32CharToRegion(region, hour%10 + 1, char_x, 0);
    char_x += 32;

    // 绘制冒号
    drawLarge32CharToRegion(region, 0, char_x, 0);  // 冒号索引为0
    char_x += 32;

    // 绘制分钟
    drawLarge32CharToRegion(region, minute/10 + 1, char_x, 0);
    char_x += 32;
    drawLarge32CharToRegion(region, minute%10 + 1, char_x, 0);
}
```

#### 2.2 32x32字体数据结构
```cpp
// 32x32字体数据定义
#define LARGE_FONT_WIDTH     32
#define LARGE_FONT_HEIGHT    32
#define LARGE_FONT_BYTES_PER_ROW 4  // 32位 = 4字节

// 32x32字体数据 (每个字符128字节)
const unsigned char large_font_data[11][128] = {
    { // 冒号 ':'
        // 第1行 (4字节)
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x03, 0xC0, 0x00,  // 第7行：上面的点
        0x00, 0x03, 0xC0, 0x00,  // 第8行
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x00, 0x00, 0x00,
        0x00, 0x03, 0xC0, 0x00,  // 第25行：下面的点
        0x00, 0x03, 0xC0, 0x00,  // 第26行
        // ... 剩余6行
    },
    { // 数字 '0'
        // 32行 × 4字节的完整字符数据
        // ... 具体的点阵数据
    },
    // ... 数字1-9的数据
};

// 32x32字符绘制函数
void drawLarge32CharToRegion(DisplayRegion* region, uint8_t char_index, int8_t x, int8_t y) {
    if (!region->visible || !dma_display || char_index >= 11) return;

    const uint8_t* char_data = large_font_data[char_index];

    for (int row = 0; row < LARGE_FONT_HEIGHT; row++) {
        for (int byte_idx = 0; byte_idx < LARGE_FONT_BYTES_PER_ROW; byte_idx++) {
            uint8_t byte_data = char_data[row * LARGE_FONT_BYTES_PER_ROW + byte_idx];

            for (int bit = 0; bit < 8; bit++) {
                int pixel_x = region->x1 + x + (byte_idx * 8) + bit;
                int pixel_y = region->y1 + y + row;

                // 边界检查
                if (pixel_x > region->x2 || pixel_y > region->y2 ||
                    pixel_x < region->x1 || pixel_y < region->y1) continue;

                // 像素绘制
                if (byte_data & (0x01 << bit)) {
                    dma_display->drawPixel(pixel_x, pixel_y, region->text_color);
                } else if (region->auto_clear) {
                    dma_display->drawPixel(pixel_x, pixel_y, region->bg_color);
                }
            }
        }
    }
}
```

### 3. 添加更多语言的完整流程

#### 3.1 第一步：扩展语言枚举
```cpp
typedef enum {
    LANG_CHINESE = 0,
    LANG_ENGLISH = 1,
    LANG_JAPANESE = 2,    // 新增日语
    LANG_KOREAN = 3,      // 新增韩语
    LANG_ARABIC = 4,      // 新增阿拉伯语
    LANG_FRENCH = 5,      // 新增法语
    LANG_COUNT
} WeekdayLanguage;
```

#### 3.2 第二步：添加新语言字体数据
```cpp
// 日语星期字体数据 (16x16点阵)
const unsigned char japanese_weekday_font_data[7][32] = {
    { // 日曜日 (にちようび) - 显示"日"
        0x00, 0x00, 0x1F, 0xF8, 0x10, 0x08, 0x10, 0x08,
        0x1F, 0xF8, 0x10, 0x08, 0x10, 0x08, 0x1F, 0xF8,
        0x10, 0x08, 0x10, 0x08, 0x10, 0x08, 0x1F, 0xF8,
        0x10, 0x08, 0x10, 0x08, 0x1F, 0xF8, 0x00, 0x00
    },
    // ... 其他6天的日语数据
};

// 韩语星期字体数据 (16x16点阵)
const unsigned char korean_weekday_font_data[7][32] = {
    { // 일요일 (日曜日) - 显示"일"
        // 韩语字符的点阵数据
    },
    // ... 其他6天的韩语数据
};

// 法语星期字体数据 (8x16点阵，显示前3个字母)
const unsigned char french_weekday_font_data[7][48] = {
    { // DIMANCHE (DIM)
        // D, I, M 三个字符的8x16点阵数据
    },
    // ... 其他6天的法语数据
};
```

#### 3.3 第三步：扩展字符绘制函数
```cpp
// 通用多语言字符绘制函数
void drawMultiLangWeekdayChar(DisplayRegion* region, uint8_t weekday, WeekdayLanguage language, int8_t x, int8_t y) {
    switch (language) {
        case LANG_CHINESE:
            drawWeekdayToRegion(region, weekday);
            break;

        case LANG_ENGLISH:
            drawEnglishWeekdayCharToRegion(region, weekday, x, y);
            break;

        case LANG_JAPANESE:
            // 绘制日语字符 (16x16)
            {
                const uint8_t* char_data = japanese_weekday_font_data[weekday];
                // 使用16x16绘制逻辑
                drawJapaneseCharToRegion(region, char_data, x, y);
            }
            break;

        case LANG_KOREAN:
            // 绘制韩语字符 (16x16)
            {
                const uint8_t* char_data = korean_weekday_font_data[weekday];
                drawKoreanCharToRegion(region, char_data, x, y);
            }
            break;

        case LANG_FRENCH:
            // 绘制法语字符 (8x16，3个字母)
            drawFrenchWeekdayCharToRegion(region, weekday, x, y);
            break;

        default:
            drawWeekdayToRegion(region, weekday);
            break;
    }
}
```

#### 3.4 第四步：更新语言名称映射
```cpp
const char* getWeekdayNameWithLang(uint8_t weekday, WeekdayLanguage language) {
    switch (language) {
        case LANG_CHINESE:
            {
                static const char* chinese_names[] = {"日", "一", "二", "三", "四", "五", "六"};
                return (weekday <= 6) ? chinese_names[weekday] : "一";
            }

        case LANG_ENGLISH:
            {
                static const char* english_names[] = {"SUN", "MON", "TUE", "WED", "THU", "FRI", "SAT"};
                return (weekday <= 6) ? english_names[weekday] : "MON";
            }

        case LANG_JAPANESE:
            {
                static const char* japanese_names[] = {"日", "月", "火", "水", "木", "金", "土"};
                return (weekday <= 6) ? japanese_names[weekday] : "月";
            }

        case LANG_KOREAN:
            {
                static const char* korean_names[] = {"일", "월", "화", "수", "목", "금", "토"};
                return (weekday <= 6) ? korean_names[weekday] : "월";
            }

        case LANG_FRENCH:
            {
                static const char* french_names[] = {"DIM", "LUN", "MAR", "MER", "JEU", "VEN", "SAM"};
                return (weekday <= 6) ? french_names[weekday] : "LUN";
            }

        default:
            return getWeekdayName(weekday);
    }
}
```

#### 3.5 第五步：添加语言配置管理
```cpp
// 全局语言设置
static WeekdayLanguage current_language = LANG_CHINESE;

// 设置系统语言
void setSystemLanguage(WeekdayLanguage language) {
    if (language < LANG_COUNT) {
        current_language = language;
        // 重新绘制所有文本显示
        setWeekdayWithLanguage(time_info.weekday, language);
        Serial.printf("System language changed to: %d\n", language);
    }
}

// 获取当前语言
WeekdayLanguage getCurrentLanguage() {
    return current_language;
}

// 循环切换语言
void switchToNextLanguage() {
    current_language = (WeekdayLanguage)((current_language + 1) % LANG_COUNT);
    setSystemLanguage(current_language);
}
```

### 4. 多语言系统使用示例

#### 4.1 基本使用
```cpp
void setup() {
    // 初始化系统
    initThreeRegionClock();

    // 设置中文星期
    setWeekdayWithLanguage(1, LANG_CHINESE);  // 显示"星期一"

    // 切换到英文
    setWeekdayWithLanguage(1, LANG_ENGLISH);  // 显示"MON"

    // 切换到日语
    setWeekdayWithLanguage(1, LANG_JAPANESE); // 显示"月"
}

void loop() {
    // 每10秒切换一次语言进行演示
    static unsigned long last_switch = 0;
    if (millis() - last_switch > 10000) {
        switchToNextLanguage();
        last_switch = millis();
    }

    updateThreeRegionClock();
}
```

#### 4.2 动态语言切换
```cpp
// 通过蓝牙命令切换语言
void handleLanguageSwitchCommand(uint8_t language_code) {
    if (language_code < LANG_COUNT) {
        setSystemLanguage((WeekdayLanguage)language_code);

        // 重新显示当前时间和星期
        drawTimeToRegion(&display_regions[REGION_TIME], time_info.hour, time_info.minute);
        setWeekdayWithLanguage(time_info.weekday, (WeekdayLanguage)language_code);
    }
}
```

### 核心优势
- **模块化设计**：字体系统独立，易于扩展和维护
- **硬件无关**：基于像素级操作，适配任何尺寸的LED屏幕
- **高度可定制**：支持自定义字体、布局和多语言显示
- **实际可用**：已集成中英文星期显示，可直接使用
- **易于扩展**：提供完整的多语言添加流程和示例代码
