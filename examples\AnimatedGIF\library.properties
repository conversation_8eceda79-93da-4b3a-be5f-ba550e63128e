name=AnimatedGIF
version=2.2.0
author=Larry <PERSON>
maintainer=<PERSON>
sentence=Universal GIF player for MCUs with at least 32K of RAM.
paragraph=Designed to provide an optimized GIF player that can run on any MCU and take advantage of file IO, LCD displays, DMA, etc by providing callback functions. You can play multi-frame GIFs stored in RAM, FLASH, SDCard or any other media you choose. Plenty of sample code is provided to demonstrate these options.
category=Display
url=https://github.com/bitbank2/AnimatedGIF
architectures=*
includes=AnimatedGIF.h
