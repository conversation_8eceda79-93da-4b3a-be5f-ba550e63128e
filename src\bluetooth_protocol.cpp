#include "bluetooth_protocol.h"

BluetoothProtocolParser::BluetoothProtocolParser()
{
    dataBuffer = new uint8_t[MAX_DATA_LENGTH];
    errorCount = 0;
    timeoutCheckCounter = 0;
    reset();
}

BluetoothProtocolParser::~BluetoothProtocolParser()
{
    delete[] dataBuffer;
}

void BluetoothProtocolParser::reset()
{
    currentState = ParseState::WAITING_HEADER1;
    command = 0;
    dataLength = 0;
    dataReceived = 0;
    timeoutCheckCounter = 0;
    frameStartTime = millis();
}

bool BluetoothProtocolParser::isFrameComplete() const
{
    return currentState == ParseState::FRAME_COMPLETE;
}

ParseResult BluetoothProtocolParser::parseByte(uint8_t byte, BluetoothFrame &frame)
{
    // 优化：每100字节检查一次超时，减少millis()调用开销
    if (++timeoutCheckCounter >= 100)
    {
        timeoutCheckCounter = 0;
        if (isFrameTimeout())
        {
            errorCount++;
            reset();
            return ParseResult::FRAME_ERROR;
        }
    }

    switch (currentState)
    {
    case ParseState::WAITING_HEADER1:
        if (byte == BT_FRAME_HEADER_1)
        {
            currentState = ParseState::WAITING_HEADER2;
            frameStartTime = millis();
        }
        break;

    case ParseState::WAITING_HEADER2:
        if (byte == BT_FRAME_HEADER_2)
        {
            currentState = ParseState::WAITING_COMMAND;
        }
        else
        {
            errorCount++;
            reset();
            return ParseResult::FRAME_ERROR;
        }
        break;

    case ParseState::WAITING_COMMAND:
        if ((byte >= BT_CMD_SET_TEXT && byte <= BT_CMD_SET_EFFECT) ||
            (byte >= BT_CMD_FILE_START && byte <= BT_CMD_PLAY_GIF))
        {
            command = byte;
            currentState = ParseState::WAITING_LENGTH_HIGH;
        }
        else
        {
            errorCount++;
            reset();
            return ParseResult::INVALID_COMMAND;
        }
        break;

    case ParseState::WAITING_LENGTH_HIGH:
        dataLength = (uint16_t)byte << 8;
        currentState = ParseState::WAITING_LENGTH_LOW;
        break;

    case ParseState::WAITING_LENGTH_LOW:
        dataLength |= byte;
        dataReceived = 0;

        if (dataLength > MAX_DATA_LENGTH)
        {
            errorCount++;
            reset();
            return ParseResult::DATA_TOO_LONG;
        }

        if (dataLength == 0)
        {
            currentState = ParseState::WAITING_TAIL1;
        }
        else
        {
            currentState = ParseState::WAITING_DATA;
        }
        break;

    case ParseState::WAITING_DATA:
        dataBuffer[dataReceived++] = byte;

        if (dataReceived >= dataLength)
        {
            currentState = ParseState::WAITING_TAIL1;
        }
        break;

    case ParseState::WAITING_TAIL1:
        if (byte == BT_FRAME_TAIL_1)
        {
            currentState = ParseState::WAITING_TAIL2;
        }
        else
        {
            errorCount++;
            reset();
            return ParseResult::FRAME_ERROR;
        }
        break;

    case ParseState::WAITING_TAIL2:
        if (byte == BT_FRAME_TAIL_2)
        {
            frame.command = command;
            frame.dataLength = dataLength;
            frame.data = dataBuffer;
            frame.isValid = true;
            frame.timestamp = millis();

            currentState = ParseState::FRAME_COMPLETE;
            return ParseResult::FRAME_COMPLETE;
        }
        else
        {
            errorCount++;
            reset();
            return ParseResult::FRAME_ERROR;
        }
        break;

    case ParseState::FRAME_COMPLETE:
        reset();
        return parseByte(byte, frame);
    }

    return ParseResult::NEED_MORE_DATA;
}

// 检查帧是否超时
bool BluetoothProtocolParser::isFrameTimeout() const
{
    return (currentState != ParseState::WAITING_HEADER1) &&
           (millis() - frameStartTime > FRAME_TIMEOUT_MS);
}

// 批量解析缓冲区数据
ParseResult BluetoothProtocolParser::parseBuffer(uint8_t *buffer, size_t length,
                                                 BluetoothFrame frames[], size_t maxFrames, size_t &frameCount)
{
    frameCount = 0;
    ParseResult lastResult = ParseResult::NEED_MORE_DATA;

    for (size_t i = 0; i < length && frameCount < maxFrames; i++)
    {
        lastResult = parseByte(buffer[i], frames[frameCount]);
        if (lastResult == ParseResult::FRAME_COMPLETE)
        {
            frameCount++;
            reset(); // 准备解析下一帧
        }
    }

    return lastResult;
}

// 获取状态字符串
String BluetoothProtocolParser::getStateString() const
{
    switch (currentState)
    {
    case ParseState::WAITING_HEADER1:
        return "WAITING_HEADER1";
    case ParseState::WAITING_HEADER2:
        return "WAITING_HEADER2";
    case ParseState::WAITING_COMMAND:
        return "WAITING_COMMAND";
    case ParseState::WAITING_LENGTH_HIGH:
        return "WAITING_LENGTH_HIGH";
    case ParseState::WAITING_LENGTH_LOW:
        return "WAITING_LENGTH_LOW";
    case ParseState::WAITING_DATA:
        return "WAITING_DATA";
    case ParseState::WAITING_TAIL1:
        return "WAITING_TAIL1";
    case ParseState::WAITING_TAIL2:
        return "WAITING_TAIL2";
    case ParseState::FRAME_COMPLETE:
        return "FRAME_COMPLETE";
    default:
        return "UNKNOWN";
    }
}

// 打印调试信息
void BluetoothProtocolParser::printDebugInfo() const
{
    Serial.printf("Parser State: %s\n", getStateString().c_str());
    Serial.printf("Command: 0x%02X\n", command);
    Serial.printf("Data Length: %d\n", dataLength);
    Serial.printf("Data Received: %d\n", dataReceived);
    Serial.printf("Error Count: %d\n", errorCount);
    Serial.printf("Frame Start Time: %d\n", frameStartTime);
}

// BluetoothFrame 方法实现
String BluetoothFrame::getTextData() const
{
    if (!isValid || data == nullptr || dataLength == 0)
        return "";
    return String((char *)data, dataLength);
}

void BluetoothFrame::getColorData(uint8_t &target, uint8_t &mode, uint8_t &r, uint8_t &g, uint8_t &b) const
{
    if (!isValid || data == nullptr || dataLength < BT_COLOR_DATA_LEN)
    {
        target = mode = r = g = b = 0;
        return;
    }
    target = data[0]; // 目标：0x01=文本，0x02=背景
    mode = data[1];   // 模式：0x01=固定色，0x02=渐变色
    g = data[2];      // GRB格式
    r = data[3];
    b = data[4];
}

uint8_t BluetoothFrame::getBrightnessData() const
{
    if (!isValid || data == nullptr || dataLength < BT_BRIGHTNESS_DATA_LEN)
        return 0;
    return data[0];
}

void BluetoothFrame::getEffectData(uint8_t &type, uint8_t &speed) const
{
    if (!isValid || data == nullptr || dataLength < BT_EFFECT_DATA_LEN)
    {
        type = speed = 0;
        return;
    }
    type = data[0];  // 特效类型
    speed = data[1]; // 速度值
}

bool BluetoothFrame::isValidCommand() const
{
    return isValid && ((command >= BT_CMD_SET_TEXT && command <= BT_CMD_SET_EFFECT) ||
                       (command >= BT_CMD_FILE_START && command <= BT_CMD_PLAY_GIF));
}

// 解析文件开始命令数据
void BluetoothFrame::getFileStartData(String &filename, uint32_t &fileSize) const
{
    if (!isValid || data == nullptr || dataLength < 5) {
        filename = "";
        fileSize = 0;
        return;
    }

    // 前4字节是文件大小（小端序）
    fileSize = data[0] | (data[1] << 8) | (data[2] << 16) | (data[3] << 24);

    // 剩余字节是文件名
    if (dataLength > 4) {
        filename = String((char*)(data + 4), dataLength - 4);
    } else {
        filename = "";
    }
}

// 解析删除文件命令数据
String BluetoothFrame::getFileDeleteData() const
{
    if (!isValid || data == nullptr || dataLength == 0)
        return "";
    return String((char*)data, dataLength);
}

// 解析播放GIF命令数据
String BluetoothFrame::getPlayGifData() const
{
    if (!isValid || data == nullptr || dataLength == 0)
        return "";
    return String((char*)data, dataLength);
}

// 解析GIF偏移量命令数据
void BluetoothFrame::getGifOffsetData(int16_t &offset_x, int16_t &offset_y) const
{
    offset_x = 0;
    offset_y = 0;

    if (!isValid || data == nullptr || dataLength < 4)
        return;

    // 解析16位有符号整数 (大端序)
    offset_x = (int16_t)((data[0] << 8) | data[1]);
    offset_y = (int16_t)((data[2] << 8) | data[3]);
}
